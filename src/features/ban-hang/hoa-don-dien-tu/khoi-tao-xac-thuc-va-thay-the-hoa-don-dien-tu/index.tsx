'use client';

import { useState } from 'react';
import { EditPrintTemplateDialog, SearchDialog, ActionBar } from './components';
import { AritoDataTables } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { useRows } from '@/hooks';

export default function KhoiTaoXacThucVaThayTheHoaDonDienTuPage() {
  const [showTables, setShowTables] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);

  const { selectedRowIndex, handleRowClick } = useRows();

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchSubmit = (filters: any) => {
    console.log('Search filters:', filters);
    setShowSearchDialog(false);
    setShowTables(true);
  };

  const handleRefresh = () => {
    // Implement refresh functionality here
  };

  const handleFixedColumns = () => {
    // Implement fixed columns functionality here
  };

  const handleExportData = () => {
    // Implement export data functionality here
  };

  const handleEditPrintTemplate = () => {
    // Implement edit print template functionality here
    setShowEditPrintTemplateDialog(true);
  };

  const tables = [
    {
      name: ``,
      rows: [],
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearchSubmit}
        />
      )}

      {showEditPrintTemplateDialog && (
        <EditPrintTemplateDialog
          open={showEditPrintTemplateDialog}
          onClose={() => setShowEditPrintTemplateDialog(false)}
        />
      )}

      {showTables && (
        <div className='w-full'>
          <ActionBar
            onSearch={handleSearch}
            onRefresh={handleRefresh}
            onExportData={handleExportData}
            onFixedColumns={handleFixedColumns}
            onEditPrintTemplate={handleEditPrintTemplate}
          />
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        </div>
      )}
    </div>
  );
}
