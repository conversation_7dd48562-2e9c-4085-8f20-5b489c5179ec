import { useState } from 'react';
import {
  getDocumentTypeColumns,
  getBookTableColumns,
  getPartnerColumns,
  getCurrencyColumns
} from './search-cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { DateRangeField } from '@/components/custom/arito/form/search-fields';
import { FormField } from '@/components/custom/arito/form/form-field';
import { verificationStatusOptions } from '../../constant';
import { Label } from '@/components/ui/label';

import type { ChungTu, QuyenChungTu, KhachHang, NgoaiTe } from '@/types/schemas';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
}

export const BasicInfoTab = ({ formMode }: BasicInfoProps) => {
  const [documentType, setDocumentType] = useState<ChungTu | null>(null);
  const [authorizationCode, setAuthorizationCode] = useState<QuyenChungTu | null>(null);
  const [customer, setCustomer] = useState<KhachHang | null>(null);
  const [currency, setCurrency] = useState<NgoaiTe | null>(null);

  return (
    <div className='p-4 md:p-6'>
      <div className='flex flex-col gap-y-4 md:gap-y-6'>
        <div className='space-y-4 md:space-y-6'>
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Xử lý</Label>
            <FormField
              type='select'
              name='xu_ly'
              disabled={formMode === 'view'}
              options={[
                { label: 'Xác thực hóa đơn mới', value: 0 },
                { label: 'Xác thực hóa đơn điều chỉnh tiền', value: 1 },
                { label: 'Thay thế hóa đơn đã xác thực', value: 2 },
                { label: 'Hủy hóa đơn đã xác thực', value: 3 }
              ]}
              defaultValue={0}
            />
          </div>
          <DateRangeField />
          {/* Document Type Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Loại chứng từ</Label>
            <SearchField<ChungTu>
              type='text'
              columnDisplay='ma_ct'
              displayRelatedField='ten_ct'
              searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
              searchColumns={getDocumentTypeColumns}
              value={documentType?.ma_ct || ''}
              onRowSelection={row => setDocumentType(row)}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục loại chứng từ'
            />
          </div>

          {/* Authorization Code Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã quyển</Label>
            <SearchField<QuyenChungTu>
              type='text'
              columnDisplay='ma_nk'
              displayRelatedField='ten_nk'
              searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
              searchColumns={getBookTableColumns}
              value={authorizationCode?.ma_nk || ''}
              onRowSelection={row => setAuthorizationCode(row)}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục quyển chứng từ'
            />
          </div>

          {/* Document Number Field */}
          <div className='flex gap-2 sm:flex-row sm:items-center'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Số c/từ(từ/đến)</Label>
              <FormField type='text' name='so_ct1' disabled={formMode === 'view'} />
            </div>
            <FormField type='text' name='so_ct2' disabled={formMode === 'view'} />
          </div>

          {/* Customer Code Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã khách hàng</Label>
            <SearchField<KhachHang>
              type='text'
              columnDisplay='customer_code'
              displayRelatedField='customer_name'
              searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
              searchColumns={getPartnerColumns}
              value={customer?.customer_code || ''}
              onRowSelection={row => setCustomer(row)}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục khách hàng'
            />
          </div>

          {/* Unit Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngoại tệ</Label>
            <SearchField<NgoaiTe>
              type='text'
              columnDisplay='ma_nt'
              displayRelatedField='ten_nt'
              searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}`}
              searchColumns={getCurrencyColumns}
              value={currency?.ma_nt || ''}
              onRowSelection={row => setCurrency(row)}
              disabled={formMode === 'view'}
              dialogTitle='Danh mục ngoại tệ'
            />
          </div>

          {/* Report Format Field */}

          <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='reportTemplate' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Trạng thái xác thực
              </Label>
              <FormField
                id='reportTemplate'
                type='select'
                name='ma_tthddt'
                disabled={formMode === 'view'}
                options={verificationStatusOptions}
                defaultValue={0}
              />
            </div>
          </div>
          <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label htmlFor='reportTemplate' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                Đơn vị
              </Label>
              <FormField
                id='companyName'
                type='select'
                name='unit_id'
                disabled={formMode === 'view'}
                options={[
                  { label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ', value: 0 }
                ]}
                defaultValue={0}
              />
            </div>
          </div>

          {/* Verification Type Field */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Lọc theo người sd</Label>
            <FormField
              type='select'
              name='user_id0'
              disabled={formMode === 'view'}
              options={[
                { label: 'Tất cả', value: 0 },
                { label: 'Lọc theo người tạo', value: 1 }
              ]}
              defaultValue={0}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
