import React from 'react';
import { BottomBar, AritoForm, AritoIcon, AritoDialog } from '@/components/custom/arito';
import { searchSchema, SearchFormValues } from '../../schemas';
import { BasicInfoTab } from './BasicInfoTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

function SearchDialog({ open, onClose, onSearch }: SearchDialogProps) {
  const handleSubmit = (data: SearchFormValues) => {
    onSearch(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọ<PERSON> chứng từ'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
          </div>
        }
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
        classNameBottomBar='relative flex w-full justify-end gap-2'
      />
    </AritoDialog>
  );
}

export default SearchDialog;
