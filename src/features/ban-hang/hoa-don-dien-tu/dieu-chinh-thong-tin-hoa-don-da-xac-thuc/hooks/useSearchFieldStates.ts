import { useState } from 'react';
import { QuyenChungTu } from '@/types/schemas/quyen-chung-tu.type';
import { KhachHang } from '@/types/schemas/khach-hang.type';
import { ChungTu } from '@/types/schemas/chung-tu.type';
import { NgoaiTe } from '@/types/schemas/ngoai-te.type';

/**
 * Hook for managing search field states in the dieu-chinh-thong-tin-hoa-don-da-xac-thuc feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // Document type state
  const [documentType, setDocumentType] = useState<ChungTu | null>(null);

  // Authorization code state
  const [authorizationCode, setAuthorizationCode] = useState<QuyenChungTu | null>(null);

  // Customer state
  const [customer, setCustomer] = useState<KhachHang | null>(null);

  // Currency state (Unit field in the original code)
  const [currency, setCurrency] = useState<NgoaiTe | null>(null);

  return {
    documentType,
    setDocumentType,
    authorizationCode,
    setAuthorizationCode,
    customer,
    setCustomer,
    currency,
    setCurrency
  };
};
