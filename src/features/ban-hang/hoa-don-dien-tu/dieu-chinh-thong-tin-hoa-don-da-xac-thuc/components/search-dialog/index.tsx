import React from 'react';
import { AritoForm, BottomBar, AritoDialog, AritoIcon } from '@/components/custom/arito';
import { searchSchema, SearchFormValues } from '../../schemas';
import { useSearchFieldStates } from '../../hooks';
import { BasicInfoTab } from './BasicInfoTab';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (filters: any) => void;
}

function SearchDialog({ open, onClose, onSearch }: SearchDialogProps) {
  const searchFieldStates = useSearchFieldStates();
  const handleSubmit = (data: SearchFormValues) => {
    console.log('search');

    onSearch(data);
    onClose();
  };
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Lọc chứng từ cần điều chỉnh'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='w-full md:min-w-[500px] lg:min-w-[600px]'
        headerFields={<BasicInfoTab formMode='add' searchFieldStates={searchFieldStates} />}
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
      />
    </AritoDialog>
  );
}

export default SearchDialog;
