import {
  getDocumentTypeColumns,
  getBookTableColumns,
  getPartnerColumns,
  getUnitColumns
} from './search-cols-definition';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { DateRangeField } from '@/components/custom/arito/form/search-fields';
import { FormField } from '@/components/custom/arito/form/form-field';
import { QuyenChungTu } from '@/types/schemas/quyen-chung-tu.type';
import { KhachHang } from '@/types/schemas/khach-hang.type';
import { ChungTu } from '@/types/schemas/chung-tu.type';
import { NgoaiTe } from '@/types/schemas/ngoai-te.type';
import { useSearchFieldStates } from '../../hooks';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates: ReturnType<typeof useSearchFieldStates>;
}

export const BasicInfoTab = ({ formMode, searchFieldStates }: BasicInfoProps) => {
  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-6'>
          <div className='space-y-4 md:space-y-6'>
            <DateRangeField />
            {/* Document Type Field */}
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Loại chứng từ</Label>
              <SearchField<ChungTu>
                type='text'
                columnDisplay='ma_ct'
                displayRelatedField='ten_ct'
                searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}`}
                searchColumns={getDocumentTypeColumns}
                value={searchFieldStates.documentType?.ma_ct || ''}
                onRowSelection={row => searchFieldStates.setDocumentType(row)}
                disabled={formMode === 'view'}
                dialogTitle='Danh mục loại chứng từ'
              />
            </div>

            {/* Authorization Code Field */}
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã quyển</Label>
              <SearchField<QuyenChungTu>
                type='text'
                columnDisplay='ma_nk'
                displayRelatedField='ten_nk'
                searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                searchColumns={getBookTableColumns}
                value={searchFieldStates.authorizationCode?.ma_nk || ''}
                onRowSelection={row => searchFieldStates.setAuthorizationCode(row)}
                disabled={formMode === 'view'}
                dialogTitle='Danh mục quyển chứng từ'
              />
            </div>

            {/* Document Number Field */}
            <div className='flex gap-2 sm:flex-row sm:items-center'>
              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Số c/từ(từ/đến)</Label>
                <FormField type='text' name='so_ct1' disabled={formMode === 'view'} />
              </div>
              <FormField type='text' name='so_ct2' disabled={formMode === 'view'} />
            </div>

            {/* Customer Code Field */}
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Mã khách hàng</Label>
              <SearchField<KhachHang>
                type='text'
                columnDisplay='customer_code'
                displayRelatedField='customer_name'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                searchColumns={getPartnerColumns}
                value={searchFieldStates.customer?.customer_code || ''}
                onRowSelection={row => searchFieldStates.setCustomer(row)}
                disabled={formMode === 'view'}
                dialogTitle='Danh mục khách hàng'
              />
            </div>

            {/* Currency Field */}
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Ngoại tệ</Label>
              <SearchField<NgoaiTe>
                type='text'
                columnDisplay='ma_nt'
                displayRelatedField='ten_nt'
                searchEndpoint={`/${QUERY_KEYS.NGOAI_TE}`}
                searchColumns={getUnitColumns}
                value={searchFieldStates.currency?.ma_nt || ''}
                onRowSelection={row => searchFieldStates.setCurrency(row)}
                disabled={formMode === 'view'}
                dialogTitle='Danh mục ngoại tệ'
              />
            </div>

            {/* Report Format Field */}

            <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label htmlFor='reportTemplate' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                  Trạng thái xác thực
                </Label>
                <FormField
                  id='reportTemplate'
                  type='select'
                  name='ma_tthddt'
                  disabled={formMode === 'view'}
                  options={[
                    { label: 'Xác thực', value: 0 },
                    { label: 'Điều chỉnh', value: 1 }
                  ]}
                  defaultValue={0}
                />
              </div>
            </div>
            <div className='flex flex-col gap-2 lg:flex-row lg:items-start'>
              <div className='flex flex-col sm:flex-row sm:items-center'>
                <Label htmlFor='reportTemplate' className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>
                  Đơn vị
                </Label>
                <FormField
                  id='reportTemplate'
                  type='select'
                  name='unit_id'
                  disabled={formMode === 'view'}
                  options={[
                    { label: '0318423416 - CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ', value: 0 }
                  ]}
                  defaultValue={0}
                />
              </div>
            </div>

            {/* Verification Type Field */}
            <div className='flex flex-col sm:flex-row sm:items-center'>
              <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Lọc theo người sd</Label>
              <FormField
                type='select'
                name='verificationType'
                disabled={formMode === 'view'}
                options={[
                  { label: 'Tất cả', value: 0 },
                  { label: 'Lọc theo người tạo', value: 1 }
                ]}
                defaultValue={0}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
