'use client';

import { useState } from 'react';
import { EditPrintTemplateDialog, SearchDialog, ActionBar, AddDialog } from './components';
import { AritoDataTables } from '@/components/custom/arito';
import { getDataTableColumns } from './cols-definition';
import { useRows } from '@/hooks';

export default function DieuChinhThongTinHoaDonDaXacThuc() {
  const [showTables, setShowTables] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(true);
  const [showEditPrintTemplateDialog, setShowEditPrintTemplateDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);

  const { selectedRowIndex, handleRowClick } = useRows();

  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchSubmit = (filters: any) => {
    setShowSearchDialog(false);
    setShowTables(true);
  };

  const handleAddSubmit = (filters: any) => {
    console.log('Add filters:', filters);
    setShowAddDialog(false);
  };

  const handleRefresh = () => {};

  const handleFixedColumns = () => {};

  const handleExportData = () => {};

  const handleEditPrintTemplate = () => {
    setShowEditPrintTemplateDialog(true);
  };

  const tables = [
    {
      name: ``,
      rows: [],
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showSearchDialog && (
        <SearchDialog
          open={showSearchDialog}
          onClose={() => setShowSearchDialog(false)}
          onSearch={handleSearchSubmit}
        />
      )}

      {showEditPrintTemplateDialog && (
        <EditPrintTemplateDialog
          open={showEditPrintTemplateDialog}
          onClose={() => setShowEditPrintTemplateDialog(false)}
        />
      )}

      {showAddDialog && (
        <AddDialog onAdd={handleAddSubmit} open={showAddDialog} onClose={() => setShowAddDialog(false)} />
      )}

      {showTables && (
        <div className='w-full'>
          <ActionBar
            onSearch={handleSearch}
            onRefresh={handleRefresh}
            onExportData={handleExportData}
            onFixedColumns={handleFixedColumns}
            onAddButtonCLick={() => setShowAddDialog(true)}
          />
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        </div>
      )}
    </div>
  );
}
