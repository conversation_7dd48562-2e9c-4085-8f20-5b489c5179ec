'use client';

import { useState } from 'react';
import { useBaoCaoTongHopHangBanTraLai, useTableData, useActionBarHandler } from './hooks';
import AritoDataTables from '@/components/custom/arito/data-tables';
import InitialSearchDialog from './components/InitialSearchDialog';
import { LoadingOverlay } from '@/components/custom/arito';
import ActionBar from './components/ActionBar';

export default function BaoCaoTongHopHangBanTraLai() {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<any>({});

  const { data, isLoading, fetchData, refreshData } = useBaoCaoTongHopHangBanTraLai(searchParams);
  const { tables, handleRowClick } = useTableData(data);
  const {
    handleSearchClick,
    handlePrintClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick,
    handleEditPrintTemplateClick
  } = useActionBarHandler({
    setInitialSearchDialogOpen,
    refreshData
  });

  const handleInitialSearchClose = () => {
    if (showTable) {
      setInitialSearchDialogOpen(false);
    }
  };

  const handleInitialSearch = async (values: any) => {
    setSearchParams(values);
    setShowTable(true);
    setInitialSearchDialogOpen(false);

    // Fetch data using the hook
    await fetchData(values);
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onPrintClick={handlePrintClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
