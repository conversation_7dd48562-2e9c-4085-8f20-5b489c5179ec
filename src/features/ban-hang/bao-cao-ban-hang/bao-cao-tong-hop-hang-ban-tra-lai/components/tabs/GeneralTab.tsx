import {
  khachHangSearchColumns,
  vatTuSearchColumns,
  groupColumns,
  regionColumns,
  nhanVienSearchColumns,
  khoHangSearchColumns
} from '@/constants';
import type { NhanVien, KhachHang, Group, KhuVuc, VatTu, KhoHang } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
interface GeneralTabProps {
  searchFieldStates: {
    employee: <PERSON><PERSON><PERSON><PERSON> | null;
    setEmployee: (employee: <PERSON><PERSON><PERSON><PERSON> | null) => void;
    customer: KhachHang | null;
    setCustomer: (customer: KhachHang | null) => void;
    customerGroup1: Group | null;
    setCustomerGroup1: (customerGroup1: Group | null) => void;
    customerGroup2: Group | null;
    setCustomerGroup2: (customerGroup2: Group | null) => void;
    customerGroup3: Group | null;
    setCustomerGroup3: (customerGroup3: Group | null) => void;
    region: KhuVuc | null;
    setRegion: (region: KhuVuc | null) => void;
    product: VatTu | null;
    setProduct: (product: VatTu | null) => void;
    productGroup1: Group | null;
    setProductGroup1: (productGroup1: Group | null) => void;
    productGroup2: Group | null;
    setProductGroup2: (productGroup2: Group | null) => void;
    productGroup3: Group | null;
    setProductGroup3: (productGroup3: Group | null) => void;
    warehouse: KhoHang | null;
    setWarehouse: (warehouse: KhoHang | null) => void;
  };
}

const GeneralTab: React.FC<GeneralTabProps> = ({ searchFieldStates }) => {
  const {
    employee,
    setEmployee,
    customer,
    setCustomer,
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,
    region,
    setRegion,
    product,
    setProduct,
    productGroup1,
    setProductGroup1,
    productGroup2,
    setProductGroup2,
    productGroup3,
    setProductGroup3,
    warehouse,
    setWarehouse
  } = searchFieldStates;

  return (
    <div className='h-96 space-y-2 overflow-y-auto p-4'>
      <div className='flex flex-col space-y-2'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã nhân viên:</Label>
          <SearchField<NhanVien>
            searchEndpoint={`/${QUERY_KEYS.NHAN_VIEN}/`}
            searchColumns={nhanVienSearchColumns}
            dialogTitle='Danh mục nhân viên'
            columnDisplay='ma_nhan_vien'
            displayRelatedField='ho_ten_nhan_vien'
            value={employee?.ma_nhan_vien || ''}
            onRowSelection={setEmployee}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã khách hàng:</Label>
          <SearchField<KhachHang>
            searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
            searchColumns={khachHangSearchColumns}
            dialogTitle='Danh mục khách hàng'
            columnDisplay='customer_code'
            displayRelatedField='customer_name'
            value={customer?.customer_code || ''}
            onRowSelection={setCustomer}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-3'>
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm khách hàng 1'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={customerGroup1?.ma_nhom || ''}
                onRowSelection={setCustomerGroup1}
              />
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm khách hàng 2'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={customerGroup2?.ma_nhom || ''}
                onRowSelection={setCustomerGroup2}
              />
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_KHACH_HANG}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm khách hàng 3'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={customerGroup3?.ma_nhom || ''}
                onRowSelection={setCustomerGroup3}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Khu vực:</Label>
          <SearchField<KhuVuc>
            searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
            searchColumns={regionColumns}
            dialogTitle='Danh mục khu vực'
            columnDisplay='rg_code'
            displayRelatedField='rgname'
            value={region?.rg_code || ''}
            onRowSelection={setRegion}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã vật tư:</Label>
          <SearchField<VatTu>
            searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
            searchColumns={vatTuSearchColumns}
            dialogTitle='Danh mục vật tư'
            columnDisplay='ma_vt'
            displayRelatedField='ten_vt'
            value={product?.ma_vt || ''}
            onRowSelection={setProduct}
          />
        </div>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại vật tư:</Label>
          <div className='flex items-center gap-4'>
            <div className=''>
              <FormField
                name='ma_lvt'
                className='w-[210px]'
                type='select'
                options={[
                  { value: '', label: 'Tất cả' },
                  { value: 'service', label: 'Dịch vụ' },
                  { value: 'material', label: 'Vật tư' },
                  { value: 'spare_part', label: 'Phụ tùng' },
                  { value: 'tool', label: 'CCLĐ' },
                  { value: 'semi_finished', label: 'Bán thành phẩm' },
                  { value: 'finished', label: 'Thành phẩm' },
                  { value: 'goods', label: 'Hàng hóa' },
                  { value: 'outsourced', label: 'Hàng gia công' }
                ]}
              />
            </div>
            <div className='whitespace-nowrap'>
              <FormField name='ton_kho_yn' type='checkbox' label='Chỉ xem vật tư có theo dõi tồn kho' />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='flex gap-3'>
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm vật tư 1'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={productGroup1?.ma_nhom || ''}
                onRowSelection={setProductGroup1}
              />
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm vật tư 2'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={productGroup2?.ma_nhom || ''}
                onRowSelection={setProductGroup2}
              />
              <SearchField<Group>
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={groupColumns}
                dialogTitle='Danh mục nhóm vật tư 3'
                columnDisplay='ma_nhom'
                displayRelatedField='ten_phan_nhom'
                value={productGroup3?.ma_nhom || ''}
                onRowSelection={setProductGroup3}
              />
            </div>
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã kho:</Label>
          <SearchField<KhoHang>
            searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
            searchColumns={khoHangSearchColumns}
            dialogTitle='Danh mục kho hàng'
            columnDisplay='ma_kho'
            displayRelatedField='ten_kho'
            value={warehouse?.ma_kho || ''}
            onRowSelection={setWarehouse}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
          <div className='w-80'>
            <FormField
              name='mau_bc'
              label=''
              type='select'
              options={[
                { value: '20', label: 'Mẫu số lượng' },
                {
                  value: '21',
                  label: 'Mẫu số lượng và giá trị'
                },
                {
                  value: '22',
                  label: 'Mẫu số lượng và giá trị ngoại tệ'
                }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
