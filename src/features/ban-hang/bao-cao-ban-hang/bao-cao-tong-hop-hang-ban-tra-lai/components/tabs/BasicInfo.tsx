import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfo = () => {
  return (
    <div className='flex flex-col space-y-2 p-4'>
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Ngày từ/đến:</Label>
        <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
      </div>
      <div className='flex items-center'>
        <Label className='w-40'>Số c/từ (từ/đến):</Label>
        <div className='flex items-center gap-2'>
          <FormField name='so_ct1' label='' type='text' />

          <FormField name='so_ct2' label='' type='text' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
