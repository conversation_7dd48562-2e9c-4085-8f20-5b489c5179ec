import { useMemo } from 'react';
import { BaoCaoTongHopHangBanTraLaiItem } from '@/types/schemas/bao-cao-tong-hop-hang-ban-tra-lai.type';
import { TableData } from '@/components/custom/arito/data-tables/types';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

// Extended interface for table display with total row support
interface BaoCaoTongHopHangBanTraLaiTableItem extends BaoCaoTongHopHangBanTraLaiItem {
  isTotal?: boolean;
}

export function useTableData(data: BaoCaoTongHopHangBanTraLaiItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const totalSlNhap = data.reduce((sum, item) => sum + (item.sl_nhap || 0), 0);
    const totalTien2 = data.reduce((sum, item) => sum + (item.tien2 || 0), 0);
    const totalThue = data.reduce((sum, item) => sum + (item.thue || 0), 0);
    const totalTienNhap = data.reduce((sum, item) => sum + (item.tien_nhap || 0), 0);
    const totalTienLai = data.reduce((sum, item) => sum + (item.tien_lai || 0), 0);

    const totalRow: BaoCaoTongHopHangBanTraLaiTableItem = {
      id: 'total',
      nhom: '',
      ma_vt: '',
      ten_vt: `Tổng cộng`,
      dvt: '',
      sl_nhap: totalSlNhap,
      gia2: 0,
      tien2: totalTien2,
      thue: totalThue,
      pt: 0,
      gia: 0,
      tien_nhap: totalTienNhap,
      tien_lai: totalTienLai,
      isTotal: true
    };

    const tableData: TableData[] = [
      {
        name: '',
        columns: [
          {
            field: 'nhom',
            headerName: 'Nhóm',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.nhom || '';
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'ma_vt',
            headerName: 'Mã vật tư',
            width: 120,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.ma_vt || '';
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'ten_vt',
            headerName: 'Tên vật tư',
            width: 250,
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'dvt',
            headerName: 'Đvt',
            width: 80,
            renderCell: (params: any) => {
              return params.row.isTotal ? '' : params.row.dvt || '';
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'sl_nhap',
            headerName: 'Số lượng',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.sl_nhap || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'gia2',
            headerName: 'Giá bán',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              const value = params.row.gia2 || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'tien2',
            headerName: 'Trả lại',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.tien2 || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'thue',
            headerName: 'Thuế',
            width: 100,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.thue || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'pt',
            headerName: 'Tổng trả lại',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              const value = params.row.pt || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'gia',
            headerName: 'Giá vốn',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              if (params.row.isTotal) return '';
              const value = params.row.gia || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'tien_nhap',
            headerName: 'Tiền vốn',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.tien_nhap || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          },
          {
            field: 'tien_lai',
            headerName: 'Lãi',
            width: 120,
            type: 'number',
            renderCell: (params: any) => {
              const value = params.row.tien_lai || 0;
              return value.toLocaleString('vi-VN');
            },
            cellClassName: (params: any) => (params.row?.isTotal ? 'font-extrabold' : '')
          }
        ],
        rows: [totalRow, ...data]
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (_params: any) => {};

  return {
    tables,
    handleRowClick
  };
}
