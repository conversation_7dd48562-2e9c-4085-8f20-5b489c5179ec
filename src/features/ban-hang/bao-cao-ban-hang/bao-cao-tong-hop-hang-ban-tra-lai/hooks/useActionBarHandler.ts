export interface UseActionBarHandlerReturn {
  handleSearchClick: () => void;
  handlePrintClick: () => void;
  handleRefreshClick: () => Promise<void>;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
  handleEditPrintTemplateClick: () => void;
}

export interface UseActionBarHandlerParams {
  setInitialSearchDialogOpen: (open: boolean) => void;
  refreshData: () => Promise<void>;
}

export function useActionBarHandler({
  setInitialSearchDialogOpen,
  refreshData
}: UseActionBarHandlerParams): UseActionBarHandlerReturn {
  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handlePrintClick = () => {};

  const handleRefreshClick = async () => {
    await refreshData();
  };

  const handleFixedColumnsClick = () => {};

  const handleExportDataClick = () => {};

  const handleEditPrintTemplateClick = () => {};

  return {
    handleSearchClick,
    handlePrintClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick,
    handleEditPrintTemplateClick
  };
}
