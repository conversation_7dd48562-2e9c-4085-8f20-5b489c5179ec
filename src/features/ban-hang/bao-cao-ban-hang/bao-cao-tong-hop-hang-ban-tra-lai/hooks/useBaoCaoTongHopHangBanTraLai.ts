import { useState, useCallback } from 'react';
import {
  BaoCaoTongHopHangBanTraLaiItem,
  BaoCaoTongHopHangBanTraLaiResponse,
  UseBaoCaoTongHopHangBanTraLaiReturn
} from '@/types/schemas/bao-cao-tong-hop-hang-ban-tra-lai.type';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

export type BaoCaoTongHopHangBanTraLaiSearchFormValues = SearchFormValues;

const generateMockData = (): BaoCaoTongHopHangBanTraLaiItem[] => {
  return [
    {
      id: '1',
      nhom: 'Nhóm 1',
      ma_vt: 'VT001',
      ten_vt: 'Laptop Dell Inspiron 15',
      dvt: 'Cái',
      sl_nhap: 2,
      gia2: 15000000,
      tien2: 30000000,
      thue: 3000000,
      pt: 10,
      gia: 14000000,
      tien_nhap: 28000000,
      tien_lai: 2000000
    },
    {
      id: '2',
      nhom: 'Nhóm 1',
      ma_vt: 'VT002',
      ten_vt: '<PERSON><PERSON><PERSON> 27 inch',
      dvt: 'Cái',
      sl_nhap: 3,
      gia2: 8000000,
      tien2: 24000000,
      thue: 2400000,
      pt: 10,
      gia: 7500000,
      tien_nhap: 22500000,
      tien_lai: 1500000
    },
    {
      id: '3',
      nhom: 'Nhóm 2',
      ma_vt: 'VT003',
      ten_vt: 'Bàn phím cơ Logitech',
      dvt: 'Cái',
      sl_nhap: 5,
      gia2: 2000000,
      tien2: 10000000,
      thue: 1000000,
      pt: 10,
      gia: 1800000,
      tien_nhap: 9000000,
      tien_lai: 1000000
    },
    {
      id: '4',
      nhom: 'Nhóm 2',
      ma_vt: 'VT004',
      ten_vt: 'Chuột không dây Microsoft',
      dvt: 'Cái',
      sl_nhap: 8,
      gia2: 800000,
      tien2: 6400000,
      thue: 640000,
      pt: 10,
      gia: 750000,
      tien_nhap: 6000000,
      tien_lai: 400000
    },
    {
      id: '5',
      nhom: 'Nhóm 3',
      ma_vt: 'VT005',
      ten_vt: 'Ổ cứng SSD Samsung 1TB',
      dvt: 'Cái',
      sl_nhap: 4,
      gia2: 3500000,
      tien2: 14000000,
      thue: 1400000,
      pt: 10,
      gia: 3200000,
      tien_nhap: 12800000,
      tien_lai: 1200000
    }
  ];
};

/**
 * Custom hook for managing BaoCaoTongHopHangBanTraLai (Comprehensive Sales Return Report) data
 *
 * This hook provides functionality to fetch comprehensive sales return report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoTongHopHangBanTraLai(
  searchParams: BaoCaoTongHopHangBanTraLaiSearchFormValues
): UseBaoCaoTongHopHangBanTraLaiReturn {
  const [data, setData] = useState<BaoCaoTongHopHangBanTraLaiItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BaoCaoTongHopHangBanTraLaiSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoTongHopHangBanTraLaiResponse>(
        '/ban-hang/bao-cao-ban-hang/bao-cao-tong-hop-hang-ban-tra-lai/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
