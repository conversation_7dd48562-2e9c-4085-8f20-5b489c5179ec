import { useState } from 'react';
import type { NhanVien, KhachHang, Group, KhuVuc, VatTu, KhoHang, TaiKhoan, Lo } from '@/types/schemas';
import type { ViTriKho } from '@/types/schemas/vi-tri-kho.type';

/**
 * Hook for managing search field states in the bao-cao-tong-hop-hang-ban-tra-lai feature
 *
 * @returns Object with states and setters for search fields
 */
export const useSearchFieldStates = () => {
  // General Tab states
  const [employee, setEmployee] = useState<NhanVien | null>(null);
  const [customer, setCustomer] = useState<KhachHang | null>(null);
  const [customerGroup1, setCustomerGroup1] = useState<Group | null>(null);
  const [customerGroup2, setCustomerGroup2] = useState<Group | null>(null);
  const [customerGroup3, setCustomerGroup3] = useState<Group | null>(null);
  const [region, setRegion] = useState<KhuVuc | null>(null);
  const [product, setProduct] = useState<VatTu | null>(null);
  const [productGroup1, setProductGroup1] = useState<Group | null>(null);
  const [productGroup2, setProductGroup2] = useState<Group | null>(null);
  const [productGroup3, setProductGroup3] = useState<Group | null>(null);
  const [warehouse, setWarehouse] = useState<KhoHang | null>(null);

  // Other Tab states
  const [transactionCode, setTransactionCode] = useState<any | null>(null);
  const [itemAccount, setItemAccount] = useState<TaiKhoan | null>(null);
  const [revenueAccount, setRevenueAccount] = useState<TaiKhoan | null>(null);
  const [costAccount, setCostAccount] = useState<TaiKhoan | null>(null);
  const [batchCode, setBatchCode] = useState<Lo | null>(null);
  const [locationCode, setLocationCode] = useState<ViTriKho | null>(null);

  // General Tab search field states
  const generalTabSearchFieldStates = {
    employee,
    setEmployee,
    customer,
    setCustomer,
    customerGroup1,
    setCustomerGroup1,
    customerGroup2,
    setCustomerGroup2,
    customerGroup3,
    setCustomerGroup3,
    region,
    setRegion,
    product,
    setProduct,
    productGroup1,
    setProductGroup1,
    productGroup2,
    setProductGroup2,
    productGroup3,
    setProductGroup3,
    warehouse,
    setWarehouse
  };

  // Other Tab search field states
  const otherTabSearchFieldStates = {
    transactionCode,
    setTransactionCode,
    itemAccount,
    setItemAccount,
    revenueAccount,
    setRevenueAccount,
    costAccount,
    setCostAccount,
    batchCode,
    setBatchCode,
    locationCode,
    setLocationCode
  };

  return {
    generalTabSearchFieldStates,
    otherTabSearchFieldStates
  };
};
