'use client';

import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { ActionBar, FormDialog, SearchDialog } from './components';
import { AllocCoeffColumns } from './cols-definition';
import { useFormState, useData } from './hooks';

export default function KhaiBaoHeSoPhanBoCCDC({ initialRows }: { initialRows: any[] }) {
  const {
    showForm,
    showDelete,
    showCopy,
    showSearch,
    formMode,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleCloseSearch,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleSearchClick
  } = useFormState();

  const {
    rows,
    selectedRowIndex,
    showData,
    handleRowClick,
    handleFormSubmit,
    handleSearchSubmit,
    handleCopySubmit,
    handleDeleteConfirm,
    handleRefreshClick,
    handleFixedColumnsClick
  } = useData(initialRows);

  const tables = [
    {
      name: 'T<PERSON>t cả',
      rows: rows,
      columns: AllocCoeffColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showSearch && <SearchDialog open={showSearch} onClose={handleCloseSearch} onAdd={handleSearchSubmit} />}
      {showForm && (
        <FormDialog open={showForm} onClose={handleCloseForm} onSubmit={handleFormSubmit} formMode={formMode} />
      )}

      {showData && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onViewClick={handleViewClick}
            onSearchClick={handleSearchClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
            onCopyToNextPeriodClick={handleSearchClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
