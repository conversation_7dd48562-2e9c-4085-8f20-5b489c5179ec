'use client';

import React from 'react';
import { AccountingObjectColumns, AttachedAccessoriesColumns, CCDCInformationColumns } from './cols-definition';
import { BasicInfoTab, GeneralInfoTab, DocumentTab, OtherTab } from './components/filter-tabs';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoForm } from '@/components/custom/arito/form';
import { ActionBar, SearchDialog } from './components';
import { useFormState, useData } from './hooks';
import { initialValues } from './schema';

const KhaiBaoThongTinCCDC = ({ initialRows }: { initialRows: any[] }) => {
  const {
    showForm,
    showDelete,
    showCopy,
    showSearch,
    formMode,
    handleCloseForm,
    handleCloseDelete,
    handleCloseCopy,
    handleCloseSearch,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick,
    handleSearchClick
  } = useFormState();

  const {
    rows,
    selectedRowIndex,
    showData,
    handleRowClick,
    handleFormSubmit,
    handleSearchSubmit,
    handleCopySubmit,
    handleDeleteConfirm,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportClick,
    handleDownloadTemplateClick,
    handleImportFromExcelClick
  } = useData(initialRows);

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: CCDCInformationColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showSearch && <SearchDialog open={showSearch} onClose={handleCloseSearch} onAdd={handleSearchSubmit} />}

      {showForm && (
        <AritoForm
          mode={formMode}
          title={formMode === 'add' ? 'Mới' : undefined}
          subTitle='Danh mục tài sản cố định'
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          initialData={initialValues}
          headerFields={<BasicInfoTab formMode={formMode} />}
          tabs={[
            {
              id: 'general',
              label: 'Thông tin chung',
              component: <GeneralInfoTab />
            },
            {
              id: 'accounting',
              label: 'Đối tượng hạch toán',
              component: (
                <div className='h-full'>
                  <>
                    <AritoInputTable
                      value={[]}
                      columns={AccountingObjectColumns}
                      mode={formMode}
                      tableActionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
                    />
                  </>
                </div>
              )
            },
            {
              id: 'document',
              label: 'Chứng từ gốc',
              component: <DocumentTab />
            },
            {
              id: 'other',
              label: 'Thông tin khác',
              component: <OtherTab formMode={formMode} />
            },
            {
              id: 'attached-accessories',
              label: 'Phụ tùng kèm theo',
              component: (
                <div className='h-full'>
                  <>
                    <AritoInputTable
                      value={[]}
                      columns={AttachedAccessoriesColumns}
                      mode={formMode}
                      tableActionButtons={['add', 'delete', 'copy', 'paste', 'moveUp', 'moveDown', 'export', 'pin']}
                    />
                  </>
                </div>
              )
            }
          ]}
        />
      )}

      {!showForm && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onSearchClick={handleSearchClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
            onExportClick={handleExportClick}
            onDownloadTemplateClick={handleDownloadTemplateClick}
            onImportFromExcelClick={handleImportFromExcelClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default KhaiBaoThongTinCCDC;
