'use client';

import { GridRowParams } from '@mui/x-data-grid';
import React, { useState } from 'react';
import { Button } from '@mui/material';
import { BasicInfoTab } from './components/filter-tabs/BasicInfoTab';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import { ValueAdjustmentColumns } from './cols-definition';
import AritoIcon from '@/components/custom/arito/icon';
import { ActionBar } from './components/ActionBar';
import { ValueAdjustmentSchema } from './schema';
import AddDialog from './components/add-dialog';

export default function DieuChinhGiaTriCCDC({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState<boolean>(true);
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObject, setCurrentObject] = useState<any>(null);
  const [showTable, setShowTable] = useState<boolean>(false);

  //Initial
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name // Use the name field as the id
    }))
  );

  const handleRowClick = (params: GridRowParams) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted with data:', data);
    setCurrentObject(data);
  };

  const handleAddFormSubmit = (data: any) => {
    console.log('Add Form submitted with data:', data);
    // TODO: Implement form submission logic
    setShowAddForm(false);
  };

  const handleDirectSubmit = () => {
    console.log('Direct submit with data:', currentObject);
    // TODO: Implement form submission logic
    setShowForm(false);
    setShowTable(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleCloseAddForm = () => {
    setShowAddForm(false);
  };

  const handleAddClick = () => {
    setShowAddForm(true);
    // TODO: Implement add logic
  };

  const handleEditClick = () => {
    console.log('Edit clicked');
    // TODO: Implement edit logic
  };

  const handleDeleteClick = () => {
    console.log('Delete clicked');
    // TODO: Implement delete logic
  };

  const handleCopyClick = () => {
    console.log('Copy clicked');
    // TODO: Implement copy logic
  };

  const handleSearchClick = () => {
    setShowForm(true);
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
    // TODO: Implement refresh logic
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns logic
  };

  const handleExportClick = () => {
    console.log('Export clicked');
    // TODO: Implement export logic
  };

  const handleDownloadTemplateClick = () => {
    console.log('Download template clicked');
    // TODO: Implement download template logic
  };

  const handleImportFromExcelClick = () => {
    console.log('Import from Excel clicked');
    // TODO: Implement import from Excel logic
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: ValueAdjustmentColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showAddForm && <AddDialog open={showAddForm} onClose={handleCloseAddForm} onAdd={handleAddFormSubmit} />}

      <AritoDialog
        open={showForm}
        onClose={handleCloseForm}
        title='Điều kiện lọc'
        maxWidth='lg'
        disableBackdropClose={false}
        disableEscapeKeyDown={false}
        titleIcon={<AritoIcon icon={12} />}
        actions={
          <>
            <Button
              variant='contained'
              onClick={handleDirectSubmit}
              className='bg-[rgba(15,118,110,0.9)] normal-case text-white hover:bg-[rgba(15,118,110,1)]'
              type='submit'
            >
              <AritoIcon icon={884} />
              <span className='ml-1'>Đồng ý</span>
            </Button>
            <Button onClick={handleCloseForm} variant='outlined'>
              <AritoIcon icon={885} />
              <span className='ml-1'>Huỷ</span>
            </Button>
          </>
        }
      >
        <AritoForm<any>
          mode={formMode}
          hasAritoActionBar={false}
          schema={ValueAdjustmentSchema}
          initialData={currentObject}
          onSubmit={handleFormSubmit}
          className='w-full'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' style={{ width: '800px', minWidth: '800px' }}>
              <BasicInfoTab formMode={formMode} />
            </div>
          }
        />
      </AritoDialog>

      {showTable && (
        <>
          <ActionBar
            onAddClick={handleAddClick}
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteClick}
            onCopyClick={handleCopyClick}
            onSearchClick={handleSearchClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onRefreshClick={handleRefreshClick}
            onExportClick={handleExportClick}
            onDownloadTemplateClick={handleDownloadTemplateClick}
            onImportFromExcelClick={handleImportFromExcelClick}
          />

          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              selectedRowId={selectedRowIndex || undefined}
              onRowClick={handleRowClick}
            />
          </div>
        </>
      )}
    </div>
  );
}
