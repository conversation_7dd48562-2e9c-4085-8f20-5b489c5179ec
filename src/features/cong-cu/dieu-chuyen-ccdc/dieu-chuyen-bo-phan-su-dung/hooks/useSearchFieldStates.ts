import { useState } from 'react';
import { TaiK<PERSON>an, BoPhanSuDungCCDC, CongCuDungCu, DieuChinhBoPhanSuDungCCDC } from '@/types/schemas';

const useSearchFieldStates = (initialData?: DieuChinhBoPhanSuDungCCDC) => {
  const [maCongCu, setMaCongCu] = useState<CongCuDungCu | null>(initialData?.ma_cc_data || null);

  const [bo<PERSON>han, setBoPhan] = useState<BoPhanSuDungCCDC | null>(initialData?.ma_bp_data || null);

  const [tkCongCu, setTkCongCu] = useState<TaiKhoan | null>(initialData?.tk_cc_data || null);

  const [tkKhauHao, setTkKhauHao] = useState<TaiKhoan | null>(initialData?.tk_kh_data || null);

  const [tkChiPhi, setTkChiPhi] = useState<TaiKhoan | null>(initialData?.tk_cp_data || null);

  return {
    maCongCu,
    setMaCongCu,
    boPhan,
    setBoPhan,
    tkCongCu,
    setTkCongCu,
    tkKhauHao,
    setTkKhauHao,
    tkChiPhi,
    setTkChiPhi
  };
};

export default useSearchFieldStates;
