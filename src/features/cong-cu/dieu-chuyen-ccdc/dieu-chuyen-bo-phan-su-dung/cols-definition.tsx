import { GridColDef } from '@mui/x-data-grid';

export const getDataTableColumns = (): GridColDef[] => [
  {
    field: 'ma_cc',
    headerName: 'Mã công cụ',
    width: 150,
    renderCell: (params: any) => {
      return params.row.ma_cc_data?.ma_cc || '';
    }
  },
  {
    field: 'ten_cc',
    headerName: 'Tên công cụ',
    width: 250,
    renderCell: (params: any) => {
      return params.row.ma_cc_data?.ten_cc || '';
    }
  },
  {
    field: 'ky',
    headerName: 'Kỳ',
    width: 70
  },
  {
    field: 'nam',
    headerName: 'Năm',
    width: 70
  },
  {
    field: 'ma_bp',
    headerName: 'Mã bộ phận',
    width: 150,
    renderCell: (params: any) => {
      return params.row.ma_bp_data?.ma_bp || '';
    }
  },
  {
    field: 'tk_cc',
    headerName: 'TK công cụ',
    width: 120,
    renderCell: (params: any) => {
      return params.row.tk_cc_data?.code || '';
    }
  },
  {
    field: 'tk_pb',
    headerName: 'TK phân bổ',
    width: 120,
    renderCell: (params: any) => {
      return params.row.tk_kh_data?.code || '';
    }
  },
  {
    field: 'tk_cp',
    headerName: 'TK chi phí',
    width: 120,
    renderCell: (params: any) => {
      return params.row.tk_cp_data?.code || '';
    }
  }
];
