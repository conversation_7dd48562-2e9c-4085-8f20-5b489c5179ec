import { Eye, FileText, Pencil, Plus, Trash, RefreshCw } from 'lucide-react';
import { AritoActionBar, AritoActionButton, AritoIcon, AritoMenuButton } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick: () => void;
}

const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Điều chuyển bộ phận sử dụng CCDC</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
    <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />

    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
