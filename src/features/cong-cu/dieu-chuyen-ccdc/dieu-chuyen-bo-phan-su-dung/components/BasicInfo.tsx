import { QUERY_KEYS, accountSearchColumns, BoPhanSuDungCCDCSearchColumns, ccdcSearchColumns } from '@/constants';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { <PERSON><PERSON><PERSON><PERSON>, BoPhanSuDungCCDC, CongCuDungCu } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
  searchFields: {
    maCongCu: CongCuDungCu | null;
    setMaCongCu: (tool: CongCuDungCu | null) => void;
    boPhan: BoPhanSuDungCCDC | null;
    setBoPhan: (department: BoPhanSuDungCCDC | null) => void;
    tkCongCu: TaiKhoan | null;
    setTkCongCu: (account: TaiKhoan | null) => void;
    tkKhauHao: <PERSON><PERSON><PERSON><PERSON> | null;
    setTkKhauHao: (account: <PERSON><PERSON><PERSON><PERSON> | null) => void;
    tkChiPhi: <PERSON><PERSON><PERSON><PERSON> | null;
    setTkChiPhi: (account: TaiKhoan | null) => void;
  };
}

const BasicInfo: React.FC<BasicInfoProps> = ({ formMode, searchFields }) => {
  const isViewMode = formMode === 'view';
  const {
    maCongCu,
    setMaCongCu,
    boPhan,
    setBoPhan,
    tkCongCu,
    setTkCongCu,
    tkKhauHao,
    setTkKhauHao,
    tkChiPhi,
    setTkChiPhi
  } = searchFields;

  return (
    <div className='max-h-[calc(100vh-150px)] space-y-4 overflow-y-auto p-6'>
      {/* Mã công cụ */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Mã công cụ</Label>
        <SearchField<CongCuDungCu>
          type='text'
          displayRelatedField={'ten_cc'}
          columnDisplay={'ma_cc'}
          searchEndpoint={`/${QUERY_KEYS.KHAI_BAO_THONG_TIN_CCDC}`}
          searchColumns={ccdcSearchColumns}
          dialogTitle='Danh mục công cụ'
          value={maCongCu?.ma_cc || ''}
          onRowSelection={setMaCongCu}
          disabled={isViewMode}
        />
      </div>

      {/* Kỳ/Năm */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Kỳ/Năm</Label>
        <div className='flex items-center gap-3'>
          <FormField type='number' name='ky' className='w-20' disabled={isViewMode} />
          <FormField type='number' name='nam' className='w-28' disabled={isViewMode} />
        </div>
      </div>

      {/* Bộ phận */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Bộ phận</Label>
        <SearchField<BoPhanSuDungCCDC>
          type='text'
          displayRelatedField={'ten_bp'}
          columnDisplay={'ma_bp'}
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_CCDC}`}
          searchColumns={BoPhanSuDungCCDCSearchColumns}
          dialogTitle='Danh mục bộ phận'
          value={boPhan?.ma_bp || ''}
          relatedFieldValue={boPhan?.ten_bp || ''}
          onRowSelection={setBoPhan}
          disabled={isViewMode}
        />
      </div>

      {/* Tk công cụ */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Tk công cụ</Label>
        <SearchField<TaiKhoan>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkCongCu?.code || ''}
          relatedFieldValue={tkCongCu?.name || ''}
          onRowSelection={setTkCongCu}
          disabled={isViewMode}
        />
      </div>

      {/* Tk khấu hao */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Tk khấu hao</Label>
        <SearchField<TaiKhoan>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkKhauHao?.code || ''}
          relatedFieldValue={tkKhauHao?.name || ''}
          onRowSelection={setTkKhauHao}
          disabled={isViewMode}
        />
      </div>

      {/* Tk chi phí */}
      <div className='flex items-center'>
        <Label className='mt-1.5 min-w-40'>Tk chi phí</Label>
        <SearchField<TaiKhoan>
          type='text'
          displayRelatedField={'name'}
          columnDisplay={'code'}
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          value={tkChiPhi?.code || ''}
          relatedFieldValue={tkChiPhi?.name || ''}
          onRowSelection={setTkChiPhi}
          disabled={isViewMode}
        />
      </div>
    </div>
  );
};

export default BasicInfo;
