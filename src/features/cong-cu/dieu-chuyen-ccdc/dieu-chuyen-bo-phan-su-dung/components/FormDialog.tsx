import { useState } from 'react';
import { AritoDialog, AritoForm, AritoIcon, BottomBar } from '@/components/custom/arito';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { DieuChinhBoPhanSuDungCCDCInput } from '@/types/schemas';
import { useSearchFieldStates } from '../hooks';
import { FormSchema } from '../schema';
import BasicInfo from './BasicInfo';

interface FormDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: DieuChinhBoPhanSuDungCCDCInput) => Promise<void>;
  formMode: 'add' | 'edit' | 'view';
  initialData?: any;
  onAddButtonClick?: () => void;
  onEditButtonClick?: () => void;
  onDeleteButtonClick?: () => void;
  onCopyButtonClick?: () => void;
}

const FormDialog = ({
  open,
  onClose,
  onSubmit,
  formMode,
  initialData,
  onAddButtonClick,
  onEditButtonClick,
  onDeleteButtonClick,
  onCopyButtonClick
}: FormDialogProps) => {
  const [error, setError] = useState<string | null>(null);

  const searchFields = useSearchFieldStates(initialData);
  const { maCongCu, boPhan, tkCongCu, tkKhauHao, tkChiPhi } = searchFields;

  const handleSubmit = async (data: any) => {
    try {
      setError(null);
      const updatedData: DieuChinhBoPhanSuDungCCDCInput = {
        ma_cc: maCongCu?.uuid || null,
        ky: data.ky || null,
        nam: data.nam || null,
        ma_bp: boPhan?.uuid || null,
        tk_cc: tkCongCu?.uuid || null,
        tk_kh: tkKhauHao?.uuid || null,
        tk_cp: tkChiPhi?.uuid || null
      };
      await onSubmit(updatedData);
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra');
    }
  };

  const title = formMode === 'add' ? 'Thêm' : formMode === 'edit' ? 'Sửa ' : 'Xem ';
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleCloseDialog = () => {
    setShowConfirmDialog(false);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      titleIcon={<AritoIcon icon={281} />}
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={FormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='min-w-[850px]'
        headerFields={
          <>
            <BasicInfo formMode={formMode} searchFields={searchFields} />
            {error && <div className='mx-4 mb-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
          </>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <BottomBar
            mode={formMode}
            onAdd={onAddButtonClick}
            onEdit={onEditButtonClick}
            onDelete={onDeleteButtonClick}
            onCopy={onCopyButtonClick}
            onClose={() => setShowConfirmDialog(true)}
          />
        }
      />

      <ConfirmationDialog
        open={showConfirmDialog}
        onClose={() => setShowConfirmDialog(false)}
        onConfirm={handleCloseDialog}
        title='Cảnh báo'
        message='Bạn muốn kết thúc?'
      />
    </AritoDialog>
  );
};

export default FormDialog;
