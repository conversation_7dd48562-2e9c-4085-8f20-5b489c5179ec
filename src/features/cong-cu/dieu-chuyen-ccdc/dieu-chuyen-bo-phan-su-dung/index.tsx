'use client';

import type { AnyAaaaRecord } from 'dns';
import { DieuChinhBoPhanSuDungCCDC, DieuChinhBoPhanSuDungCCDCInput } from '@/types/schemas';
import ConfirmationDialog from '@/components/custom/arito/dialog/confirm-dialog';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDieuChinhBoPhanSuDungCCDC, useRows } from '@/hooks';
import { getDataTableColumns } from './cols-definition';
import { ActionBar, FormDialog } from './components';
import { initialFormValues } from './schema';
import { useFormState } from '@/hooks';

export default function DieuChuyenBoPhanSuDungPage() {
  const {
    dieuChinhBoPhanSuDungCCDCs,
    isLoading,
    addDieuChinhBoPhanSuDungCCDC,
    updateDieuChinhBoPhanSuDungCCDC,
    deleteDieuChinhBoPhanSuDungCCDC,
    refreshDieuChinhBoPhanSuDungCCDC
  } = useDieuChinhBoPhanSuDungCCDC();

  const {
    showForm,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick
  } = useFormState();

  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRows<DieuChinhBoPhanSuDungCCDC>();

  const handleFormSubmit = async (data: DieuChinhBoPhanSuDungCCDCInput) => {
    if (formMode === 'add') {
      await addDieuChinhBoPhanSuDungCCDC(data);
    } else if (formMode === 'edit' && selectedObj && selectedObj.uuid) {
      await updateDieuChinhBoPhanSuDungCCDC(selectedObj.uuid, data);
    }
    handleCloseForm();
    clearSelection();
  };

  const tables = [
    {
      name: '',
      rows: dieuChinhBoPhanSuDungCCDCs,
      columns: getDataTableColumns()
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      <div className='w-full'>
        <ActionBar
          onAddClick={handleAddClick}
          onEditClick={() => selectedObj && handleEditClick()}
          onDeleteClick={() => selectedObj && handleDeleteClick()}
          onCopyClick={() => selectedObj && handleCopyClick()}
          onViewClick={() => selectedObj && handleViewClick()}
          onRefreshClick={() => refreshDieuChinhBoPhanSuDungCCDC()}
        />

        {isLoading && <LoadingOverlay />}

        {!isLoading && (
          <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
        )}
      </div>

      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          formMode={formMode}
          initialData={formMode === 'add' && !isCopyMode ? initialFormValues : selectedObj}
          onAddButtonClick={() => {
            handleCloseForm();
            clearSelection();
            handleAddClick();
          }}
          onEditButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleEditClick();
            }
          }}
          onDeleteButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleDeleteClick();
            }
          }}
          onCopyButtonClick={() => {
            if (selectedObj) {
              handleCloseForm();
              handleCopyClick();
            }
          }}
        />
      )}

      <ConfirmationDialog
        open={showDelete}
        onClose={handleCloseDelete}
        onConfirm={() => {
          deleteDieuChinhBoPhanSuDungCCDC(selectedObj!.uuid);
          handleCloseDelete();
          clearSelection();
        }}
      />
    </div>
  );
}
