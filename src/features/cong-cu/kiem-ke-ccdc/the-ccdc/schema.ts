import { z } from 'zod';

export const CCDCToolCardSchema = z.object({
  // Basic Info tab - matching backend API field names
  ky: z.coerce.number().min(1, { message: '<PERSON><PERSON> phải lớn hơn 0' }),
  nam: z.coerce.number().min(2000, { message: 'Năm phải lớn hơn 2000' }),

  // Detail tab - matching backend API field names
  mau_bc: z.coerce.number().optional(),

  data_analysis_struct: z.string().optional()
});
export type CCDCToolCard = z.infer<typeof CCDCToolCardSchema>;

export const initialValues: CCDCToolCard = {
  ky: 5,
  nam: new Date().getFullYear(),
  mau_bc: 10,
  data_analysis_struct: ''
};
