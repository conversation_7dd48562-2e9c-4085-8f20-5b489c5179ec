import { <PERSON>T<PERSON><PERSON>, Lock, Printer, RefreshCw, Search } from 'lucide-react';
import { AritoMenuButton, AritoActionBar } from '@/components/custom/arito';
import { AritoActionButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onPrintClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onEditPrintTemplateClick?: () => void;
  subTitle?: string;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onFixedColumnsClick,
  onExportClick,
  onEditPrintTemplateClick,
  subTitle = ''
}) => (
  <AritoActionBar
    titleComponent={
      <h1 className='text-xl font-bold'>
        Thẻ công cụ dụng cụ{' '}
        <div className='text-[10px] text-gray-500'>
          <p>
            <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600' />
            <span className='font-semibold'>{subTitle}</span>
          </p>
        </div>
      </h1>
    }
  >
    {onSearchClick && <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} />}
    {onPrintClick && (
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Thẻ công cụ dụng cụ',
            icon: <AritoIcon icon={569} />,
            onClick: onPrintClick,
            group: 0
          }
        ]}
      />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}
    {onFixedColumnsClick && (
      <AritoActionButton title='Cố định cột' icon={Lock} onClick={onFixedColumnsClick} disabled={isViewDisabled} />
    )}
    {onExportClick && (
      <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={onExportClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: onEditPrintTemplateClick,
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
