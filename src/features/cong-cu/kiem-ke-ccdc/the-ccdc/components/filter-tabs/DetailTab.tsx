'use client';

import { bodyPartSearchColumns, toolCodeSearchColumns, toolTypeSearchColumns } from '../../cols-definition';
import { BoPhanSuDungCCDC, Group, LoaiTaiSanCongCu, KhaiBaoThongTinCCDC } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { groupColumns } from '@/constants/search-columns';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
  searchFieldStates: {
    toolCode: KhaiBaoThongTinCCDC | null;
    setToolCode: (toolCode: KhaiBaoThongTinCCDC | null) => void;
    toolType: LoaiTaiSanCongCu | null;
    setToolType: (toolType: LoaiTaiSanCongCu | null) => void;
    bodyPart: BoPhanSuDungCCDC | null;
    setBodyPart: (bodyPart: BoPhanSuDungCCDC | null) => void;
    toolGroup1: Group | null;
    setToolGroup1: (group: Group | null) => void;
    toolGroup2: Group | null;
    setToolGroup2: (group: Group | null) => void;
    toolGroup3: Group | null;
    setToolGroup3: (group: Group | null) => void;
  };
}

const DetailTab: React.FC<DetailTabProps> = ({ formMode, searchFieldStates }) => {
  return (
    <div className='p-4'>
      <div className='flex flex-col space-y-4'>
        <div className='space-y-4'>
          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mã công cụ:</Label>
            <SearchField<KhaiBaoThongTinCCDC>
              className='w-[250px]'
              disabled={formMode === 'view'}
              searchColumns={toolCodeSearchColumns}
              type='text'
              displayRelatedField='dien_giai'
              columnDisplay='dien_giai'
              searchEndpoint={`/${QUERY_KEYS.CONG_CU_DUNG_CU}`}
              dialogTitle='Danh mục mã công cụ'
              placeholder='Chọn mã công cụ'
              value={searchFieldStates.toolCode?.dien_giai || ''}
              relatedFieldValue={searchFieldStates.toolCode?.dien_giai || ''}
              onRowSelection={searchFieldStates.setToolCode}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Loại công cụ:</Label>
            <SearchField<LoaiTaiSanCongCu>
              className='w-[250px]'
              disabled={formMode === 'view'}
              searchColumns={toolTypeSearchColumns}
              type='text'
              displayRelatedField='ten_lts'
              columnDisplay='ten_lts'
              searchEndpoint={`/${QUERY_KEYS.LOAI_TAI_SAN_CONG_CU}`}
              dialogTitle='Danh mục loại công cụ'
              placeholder='Chọn loại công cụ'
              value={searchFieldStates.toolType?.ten_lts || ''}
              relatedFieldValue={searchFieldStates.toolType?.ten_lts || ''}
              onRowSelection={searchFieldStates.setToolType}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Bộ phận sử dụng:</Label>
            <SearchField<BoPhanSuDungCCDC>
              className='w-[250px]'
              disabled={formMode === 'view'}
              searchColumns={bodyPartSearchColumns}
              type='text'
              displayRelatedField='ten_bp'
              columnDisplay='ten_bp'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN_SU_DUNG_CCDC}`}
              dialogTitle='Danh mục bộ phận'
              placeholder='Chọn bộ phận'
              value={searchFieldStates.bodyPart?.ten_bp || ''}
              relatedFieldValue={searchFieldStates.bodyPart?.ten_bp || ''}
              onRowSelection={searchFieldStates.setBodyPart}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Nhóm công cụ 1,2,3:</Label>
            <SearchField<Group>
              className='w-[150px]'
              disabled={formMode === 'view'}
              searchColumns={groupColumns}
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC1`}
              dialogTitle='Danh mục nhóm công cụ'
              placeholder='Nhóm 1'
              value={searchFieldStates.toolGroup1?.ten_phan_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup1?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup1}
            />

            <SearchField<Group>
              className='ml-2 w-[150px]'
              disabled={formMode === 'view'}
              searchColumns={groupColumns}
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC2`}
              dialogTitle='Danh mục nhóm công cụ'
              placeholder='Nhóm 2'
              value={searchFieldStates.toolGroup2?.ten_phan_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup2?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup2}
            />

            <SearchField<Group>
              className='ml-2 w-[150px]'
              disabled={formMode === 'view'}
              searchColumns={groupColumns}
              type='text'
              displayRelatedField='ten_phan_nhom'
              columnDisplay='ten_phan_nhom'
              searchEndpoint={`/${QUERY_KEYS.NHOM}?loai_nhom=CC3`}
              dialogTitle='Danh mục nhóm công cụ'
              placeholder='Nhóm 3'
              value={searchFieldStates.toolGroup3?.ten_phan_nhom || ''}
              relatedFieldValue={searchFieldStates.toolGroup3?.ten_phan_nhom || ''}
              onRowSelection={searchFieldStates.setToolGroup3}
            />
          </div>

          <div className='flex items-center'>
            <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
            <FormField
              className='w-32 min-w-[300px]'
              type='select'
              label=''
              name='mau_bc'
              options={[{ label: 'Mẫu chuẩn', value: 'standard_sample' }]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
