'use client';

import { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SaveTemplateDialog } from '../../components';
import { Label } from '@/components/ui/label';

interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const OtherTab: React.FC = () => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    setSaveFilterTemplateDialogOpen(false);
  };

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='p-4'>
      <div className='flex flex-col space-y-4'>
        <div className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
            <FormField
              className='w-32 min-w-[300px]'
              type='select'
              label=''
              name='ma_unit'
              options={[
                {
                  label: 'Người dùng tự lọc',
                  value: 'user_filter'
                }
              ]}
            />
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu lọc',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>

          <div className='flex items-center gap-2'>
            <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
            <FormField
              className='w-32 min-w-[300px]'
              type='select'
              label=''
              name='data_analysis_struct'
              options={[
                {
                  label: 'Không phân tích',
                  value: 'no_analysis'
                }
              ]}
            />
            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={873}
                items={[
                  {
                    value: 'save_new',
                    label: 'Tạo mới mẫu phân tích',
                    icon: 7,
                    onClick: () => setSaveAnalysisTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
};

export default OtherTab;
