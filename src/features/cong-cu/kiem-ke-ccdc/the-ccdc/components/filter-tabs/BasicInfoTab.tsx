'use client';

import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center gap-2'>
          <Label className='w-40 min-w-40'>Kỳ/Năm</Label>
          <FormField className='mr-2 w-24 min-w-[50px]' type='number' label='' name='ky' />
          <FormField className='w-32 min-w-[50px]' type='number' label='' name='nam' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
