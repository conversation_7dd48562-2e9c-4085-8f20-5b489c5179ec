import React from 'react';
import { AritoDialog, AritoForm, AritoHeaderTabs, AritoIcon, BottomBar } from '@/components/custom/arito';
import { BasicInfoTab, DetailTab, OtherTab } from '../components/filter-tabs';
import { CCDCToolCardSchema, initialValues } from '../schema';
import { useSearchFieldStates } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const searchFieldStates = useSearchFieldStates();

  const handleFormSubmit = (data: any) => {
    console.log('Search form submitted:', data);
    onSearch(data);
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Thẻ CCDC - T<PERSON><PERSON> kiếm'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm<any>
        mode='add'
        hasAritoActionBar={false}
        schema={CCDCToolCardSchema}
        initialData={initialValues}
        onSubmit={handleFormSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfoTab />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'detail',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' searchFieldStates={searchFieldStates} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={<BottomBar onClose={onClose} mode='add' />}
      />
    </AritoDialog>
  );
};
