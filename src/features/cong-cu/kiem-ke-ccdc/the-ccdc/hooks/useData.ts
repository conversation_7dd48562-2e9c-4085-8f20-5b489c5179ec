import { useState } from 'react';

const useData = (initialRows: any[]) => {
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name // Use the name field as the id
    }))
  );
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [currentObject, setCurrentObject] = useState<any>(null);

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = (data: any) => {
    console.log('Form submitted:', data);
    // TODO: Implement form submission logic
  };

  const handlePrintClick = () => {
    console.log('Print clicked');
    // TODO: Implement print logic
  };

  const handleRefreshClick = () => {
    console.log('Refresh clicked');
    // TODO: Implement refresh logic
  };

  const handleFixedColumnsClick = () => {
    console.log('Fixed columns clicked');
    // TODO: Implement fixed columns logic
  };

  const handleExportDataClick = () => {
    console.log('Export data clicked');
    // TODO: Implement export data logic
  };

  return {
    rows,
    selectedRowIndex,
    currentObject,
    handleRowClick,
    handleFormSubmit,
    handlePrintClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
};

export default useData;
