import { useMemo } from 'react';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { CCDCToolCardColumns } from '../cols-definition';
import { TheCCDCItem } from '@/types/schemas';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: TheCCDCItem[]): UseTableDataReturn {
  const tables = useMemo(() => {
    const tableData: TableData[] = [
      {
        name: '',
        columns: CCDCToolCardColumns,
        rows: data.map(item => ({
          id: item.id,
          stt: item.stt,
          ma_cc: item.ma_cc,
          ten_cc: item.ten_cc,
          ngay_mua: item.ngay_mua,
          ngay_kh0: item.ngay_kh0,
          so_ky_kh: item.so_ky_kh,
          tk_cc: item.tk_cc,
          tk_kh: item.tk_kh,
          tk_cp: item.tk_cp,
          ma_bp: item.ma_bp,
          ten_bp: item.ten_bp,
          nh_cc1: item.nh_cc1,
          nh_cc2: item.nh_cc2,
          nh_cc3: item.nh_cc3,
          nuoc_sx: item.nuoc_sx,
          nam_sx: item.nam_sx,
          ngay_ct: item.ngay_ct,
          ngay_giam: item.ngay_giam,
          so_ct: item.so_ct,
          so_ct_giam: item.so_ct_giam,
          ly_do_giam: item.ly_do_giam,
          ngay_dc: item.ngay_dc,
          ly_do_dc: item.ly_do_dc,
          so_hieu_cc: item.so_hieu_cc,
          ma_nt: item.ma_nt
        }))
      }
    ];

    return tableData;
  }, [data]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
