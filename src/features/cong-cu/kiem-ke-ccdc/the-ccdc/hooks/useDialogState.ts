import { useState } from 'react';

export interface SearchParams {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  showTable: boolean;
  showForm: boolean;
  formMode: 'add' | 'edit' | 'view';
  showEditPrint: boolean;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchParams, fetchData?: (params: SearchParams) => Promise<void>) => void;
  handleSearchClick: () => void;
  handleCloseForm: () => void;
  handleEditPrintClick: () => void;
  handleEditPrintClose: () => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [showEditPrint, setShowEditPrint] = useState(false);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = async (values: SearchParams, fetchData?: (params: SearchParams) => Promise<void>) => {
    if (fetchData) {
      try {
        await fetchData(values);
      } catch (error) {
        console.error('❌ Error fetching data:', error);
      }
    }

    // Show the table and close the dialog
    setShowTable(true);
    setInitialSearchDialogOpen(false);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleEditPrintClick = () => {
    setShowEditPrint(true);
  };

  const handleEditPrintClose = () => {
    setShowEditPrint(false);
  };

  return {
    initialSearchDialogOpen,
    showTable,
    showForm,
    formMode,
    showEditPrint,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleCloseForm,
    handleEditPrintClick,
    handleEditPrintClose,
    setShowTable
  };
}
