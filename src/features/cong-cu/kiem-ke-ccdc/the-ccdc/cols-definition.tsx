import { GridColDef } from '@mui/x-data-grid';
import { ExtendedGridColDef } from '@/components/custom/arito/search-table';
import { Checkbox } from '@/components/ui/checkbox';

export const CCDCToolCardColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    sortable: false,
    disableColumnMenu: true,
    align: 'center',
    headerAlign: 'center',
    renderHeader: () => (
      <div className='flex h-full items-start justify-center pb-7'>
        <Checkbox onChange={e => {}} />
      </div>
    )
  },
  { field: 'stt', headerName: 'Stt', width: 100 },
  { field: 'ma_cc', headerName: 'Mã công cụ', width: 150 },
  { field: 'ten_cc', headerName: 'Tên công cụ', width: 300 },
  { field: 'ngay_mua', headerName: 'Ngày tăng', width: 100 },
  { field: 'ngay_kh0', headerName: '<PERSON><PERSON>y tính pb', width: 100 },
  { field: 'so_ky_kh', headerName: 'Số kỳ phân bổ', width: 100 },
  { field: 'tk_cc', headerName: 'Tk công cụ', width: 100 },
  { field: 'tk_kh', headerName: 'Tk phân bổ', width: 100 },
  { field: 'tk_cp', headerName: 'Tk chi phí', width: 100 },
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 100 }
];

export const printColumns: GridColDef[] = [
  { field: 'columnName', headerName: 'Tên cột', width: 150, editable: true },
  {
    field: 'englishName',
    headerName: 'Tên tiếng Anh',
    width: 150,
    editable: true
  },
  {
    field: 'width',
    headerName: 'Độ rộng',
    width: 100,
    editable: true,
    type: 'number'
  },
  {
    field: 'format',
    headerName: 'Định dạng',
    width: 120,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Text', 'Number', 'Currency', 'Date', 'Percentage']
  },
  {
    field: 'alignment',
    headerName: 'Căn chỉnh',
    width: 100,
    editable: true,
    type: 'singleSelect',
    valueOptions: ['Left', 'Center', 'Right']
  },
  {
    field: 'bold',
    headerName: 'In đậm',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'italic',
    headerName: 'Nghiêng',
    width: 80,
    editable: true,
    type: 'boolean'
  },
  {
    field: 'total',
    headerName: 'Tổng',
    width: 80,
    editable: true,
    type: 'boolean'
  }
];

export const bodyPartSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', flex: 1 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', flex: 1 }
];

export const toolCodeSearchColumns: ExtendedGridColDef[] = [
  { field: 'uuid', headerName: 'Mã công cụ', flex: 1 },
  { field: 'dien_giai', headerName: 'Tên công cụ', flex: 1 }
];

export const toolGroupSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_nhom', headerName: 'Mã nhóm', flex: 1 },
  { field: 'ten_phan_nhom', headerName: 'Tên nhóm', flex: 1 }
];

export const toolTypeSearchColumns: ExtendedGridColDef[] = [
  { field: 'ma_lts', headerName: 'Loại công cụ', flex: 1 },
  { field: 'ten_lts', headerName: 'Tên loại công cụ', flex: 1 }
];

export const availableFields = [
  'Stt',
  'Mã công cụ',
  'Tên công cụ',
  'Ngày tăng',
  'Ngày tính pb',
  'Số kỳ phân bổ',
  'Tk công cụ',
  'Tk phân bổ',
  'Tk chi phí',
  'Mã bộ phận',
  'Mã vụ việc',
  'Mã phí'
];
