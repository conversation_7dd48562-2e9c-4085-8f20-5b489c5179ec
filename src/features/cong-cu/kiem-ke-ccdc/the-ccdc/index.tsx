'use client';

import { useState } from 'react';
import { InitialSearchDialog, ActionBar, EditPrintTemplateDialog } from './components';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import { useTheCCDC } from '@/hooks';

export default function TheCCDCPage() {
  const {
    initialSearchDialogOpen,
    showTable,
    showEditPrint,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintClick,
    handleEditPrintClose
  } = useDialogState();

  const { handleRefreshClick, handleFixedColumnsClick, handlePrintClick, handleExportDataClick } = useActionHandlers();

  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useTheCCDC(searchParams);
  const { tables, handleRowClick } = useTableData(data);

  const handleSearchWithData = async (values: any) => {
    setSearchParams(values);
    handleInitialSearch(values, fetchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleSearchWithData}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onPrintClick={handlePrintClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintClick}
            subTitle={`Kỳ ${searchParams.ky} năm ${searchParams.nam}`}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}

      {showEditPrint && (
        <EditPrintTemplateDialog open={showEditPrint} onClose={handleEditPrintClose} onSave={() => {}} />
      )}
    </div>
  );
}
