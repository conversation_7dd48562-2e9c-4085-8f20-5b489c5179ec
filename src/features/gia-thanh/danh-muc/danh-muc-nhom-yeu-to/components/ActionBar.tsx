import { Pen<PERSON><PERSON>, Trash, Copy, FilePlus2 } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import { groupTypes } from '../../danh-muc-nhom-yeu-to';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onViewClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onExportExcelClick?: () => void;
  onImportExcelClick?: () => void;
  activeGroup?: string;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onExportExcelClick,
  onImportExcelClick,
  activeGroup
}) => {
  const activeGroupInfo = groupTypes.find(group => group.value === activeGroup);
  const displayText = activeGroupInfo
    ? `Phân nhóm: ${activeGroupInfo.shortCode} - ${activeGroupInfo.label}`
    : 'Phân nhóm: YT - Nhóm yếu tố (YT)';

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='text-xl font-bold'>Danh mục phân nhóm</h1>
          <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
            <div className='mr-2 size-2 rounded-full bg-red-500' />
            Phân nhóm: {displayText}
          </span>
        </div>
      }
    >
      {onAddClick && <AritoActionButton title='Thêm' icon={FilePlus2} onClick={onAddClick} />}
      {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
      {onDeleteClick && (
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />
      )}
      {onCopyClick && (
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />
      )}

      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Xem',
            icon: <AritoIcon icon={19} />,
            onClick: onViewClick,
            group: 0
          },
          {
            title: 'Tìm kiếm',
            icon: <AritoIcon icon={12} />,
            onClick: onSearchClick,
            group: 1
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 2
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 2
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportClick,
            group: 3
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: onExportExcelClick,
            group: 4
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: onImportExcelClick,
            group: 4
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
