'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { BasicInformationTab, DetailTab, OtherTab } from '../components/AddingTab';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import { costReportByElementGroupColumns } from '../cols-definition';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { costReportByElementGroupFilterSchema } from '../schemas';
import { ActionBar } from '../components/ActionBar';
import { formatYyyyMmDd } from '@/lib/utils';

export default function CostReportByElementGroupClientPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<0 | 1>(0);

  // Data loading state
  const [loading, setLoading] = useState<boolean>(false);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  //Initial
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name || row.so_chung_tu || row.ma_chung_tu // Use the name or document number field as the id
    }))
  );

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const invoice = params.row as any;
    setSelectedObj(invoice);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = async (data: any) => {
    if (data.tu_ngay && data.den_ngay) {
      console.log(`Date Range: ${data.tu_ngay} - ${data.den_ngay}`);
    }

    setSelectedTable(data.mau_bao_cao);
    setLoading(true);

    // Here you would fetch the invoice data based on form criteria
    setTimeout(() => {
      // Simulate data fetching
      setRows([]);
      setLoading(false);
    }, 1000);

    setShowForm(false);
  };

  let tables = [
    {
      name: 'Bảng kê hóa đơn mua hàng',
      rows: rows,
      columns: costReportByElementGroupColumns
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoPopupForm<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Bảng kê hóa đơn mua hàng'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={costReportByElementGroupFilterSchema}
        headerFields={<BasicInformationTab formMode={formMode} />}
        maxWidth='md'
        fullWidth={true}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        onDeleteClick={() => {}}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
