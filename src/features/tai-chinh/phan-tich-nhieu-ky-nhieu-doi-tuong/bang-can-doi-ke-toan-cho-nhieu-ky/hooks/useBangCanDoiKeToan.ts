import { useState, useCallback } from 'react';
import type { BangCanDoiKeToanItem, BangCanDoiKeToanResponse, UseBangCanDoiKeToanReturn } from '@/types/schemas';
import { searchSchema, type BalanceSheetMultiPeriodsSchema } from '../schema';
import { getLastDayOfMonth } from '../utils/date-utils';
import api from '@/lib/api';

const generateMockData = (formData: BalanceSheetMultiPeriodsSchema): BangCanDoiKeToanItem[] => {
  const { ky, nam, loai_ky, so_ky } = formData;

  // Generate dynamic period field names based on form data
  const periodFields: string[] = [];

  switch (loai_ky) {
    case 'month': {
      let startMonth = Number(ky);
      let startYear = Number(nam);

      for (let i = 0; i < Number(so_ky); i++) {
        let calcMonth = startMonth + i;
        let calcYear = startYear;

        while (calcMonth > 12) {
          calcMonth -= 12;
          calcYear++;
        }

        const lastDay = getLastDayOfMonth(calcYear, calcMonth - 1);
        periodFields.push(`period_${i + 1}`);
      }
      break;
    }

    case 'quarter': {
      const quarterEndMonths = [3, 6, 9, 12];
      const currentMonth = new Date().getMonth() + 1;
      const currentQuarter = Math.ceil(currentMonth / 3);

      let calcQuarter = currentQuarter;
      let calcYear = Number(nam);

      for (let i = 0; i < Number(so_ky); i++) {
        periodFields.push(`period_${i + 1}`);

        calcQuarter++;
        if (calcQuarter > 4) {
          calcQuarter = 1;
          calcYear++;
        }
      }
      break;
    }

    case 'halfYear': {
      for (let i = 0; i < Number(so_ky); i++) {
        periodFields.push(`period_${i + 1}`);
      }
      break;
    }

    case 'year': {
      for (let i = 0; i < Number(so_ky); i++) {
        periodFields.push(`period_${i + 1}`);
      }
      break;
    }

    default: {
      for (let i = 0; i < Number(so_ky); i++) {
        periodFields.push(`period_${i + 1}`);
      }
    }
  }

  // Base balance sheet data structure
  const baseData = [
    {
      id: '1',
      ma_so: '100',
      chi_tieu: 'TÀI SẢN NGẮN HẠN',
      systotal: 15420000000
    },
    {
      id: '2',
      ma_so: '110',
      chi_tieu: 'Tiền và các khoản tương đương tiền',
      systotal: 2850000000
    },
    {
      id: '3',
      ma_so: '111',
      chi_tieu: 'Tiền',
      systotal: 850000000
    },
    {
      id: '4',
      ma_so: '112',
      chi_tieu: 'Các khoản tương đương tiền',
      systotal: 2000000000
    },
    {
      id: '5',
      ma_so: '120',
      chi_tieu: 'Đầu tư tài chính ngắn hạn',
      systotal: 1500000000
    },
    {
      id: '6',
      ma_so: '121',
      chi_tieu: 'Chứng khoán kinh doanh',
      systotal: 800000000
    },
    {
      id: '7',
      ma_so: '122',
      chi_tieu: 'Dự phòng giảm giá chứng khoán kinh doanh',
      systotal: -50000000
    },
    {
      id: '8',
      ma_so: '123',
      chi_tieu: 'Đầu tư nắm giữ đến ngày đáo hạn',
      systotal: 750000000
    },
    {
      id: '9',
      ma_so: '130',
      chi_tieu: 'Các khoản phải thu ngắn hạn',
      systotal: 8570000000
    },
    {
      id: '10',
      ma_so: '131',
      chi_tieu: 'Phải thu của khách hàng',
      systotal: 6200000000
    },
    {
      id: '11',
      ma_so: '132',
      chi_tieu: 'Trả trước cho người bán',
      systotal: 1200000000
    },
    {
      id: '12',
      ma_so: '133',
      chi_tieu: 'Phải thu nội bộ ngắn hạn',
      systotal: 0
    },
    {
      id: '13',
      ma_so: '134',
      chi_tieu: 'Phải thu theo tiến độ kế hoạch hợp đồng xây dựng',
      systotal: 0
    },
    {
      id: '14',
      ma_so: '135',
      chi_tieu: 'Phải thu về cho vay ngắn hạn',
      systotal: 500000000
    },
    {
      id: '15',
      ma_so: '136',
      chi_tieu: 'Phải thu ngắn hạn khác',
      systotal: 670000000
    },
    {
      id: '16',
      ma_so: '137',
      chi_tieu: 'Dự phòng phải thu ngắn hạn khó đòi',
      systotal: 0
    },
    {
      id: '17',
      ma_so: '140',
      chi_tieu: 'Hàng tồn kho',
      systotal: 2500000000
    },
    {
      id: '18',
      ma_so: '141',
      chi_tieu: 'Hàng tồn kho',
      systotal: 2600000000
    },
    {
      id: '19',
      ma_so: '149',
      chi_tieu: 'Dự phòng giảm giá hàng tồn kho',
      systotal: -100000000
    },
    {
      id: '20',
      ma_so: '150',
      chi_tieu: 'Tài sản ngắn hạn khác',
      systotal: 0
    },
    {
      id: '21',
      ma_so: '200',
      chi_tieu: 'TÀI SẢN DÀI HẠN',
      systotal: 8580000000
    },
    {
      id: '22',
      ma_so: '210',
      chi_tieu: 'Các khoản phải thu dài hạn',
      systotal: 850000000
    },
    {
      id: '23',
      ma_so: '211',
      chi_tieu: 'Phải thu dài hạn của khách hàng',
      systotal: 600000000
    },
    {
      id: '24',
      ma_so: '212',
      chi_tieu: 'Trả trước cho người bán dài hạn',
      systotal: 150000000
    },
    {
      id: '25',
      ma_so: '213',
      chi_tieu: 'Vốn kinh doanh ở đơn vị trực thuộc',
      systotal: 0
    },
    {
      id: '26',
      ma_so: '214',
      chi_tieu: 'Phải thu nội bộ dài hạn',
      systotal: 0
    },
    {
      id: '27',
      ma_so: '218',
      chi_tieu: 'Phải thu dài hạn khác',
      systotal: 100000000
    },
    {
      id: '28',
      ma_so: '220',
      chi_tieu: 'Tài sản cố định',
      systotal: 6800000000
    },
    {
      id: '29',
      ma_so: '221',
      chi_tieu: 'Tài sản cố định hữu hình',
      systotal: 6200000000
    },
    {
      id: '30',
      ma_so: '222',
      chi_tieu: 'Tài sản cố định thuê tài chính',
      systotal: 0
    }
  ];

  // Add dynamic period data to each item
  return baseData.map(item => {
    const dynamicData: any = { ...item };

    // Generate random values for each period
    periodFields.forEach((field, index) => {
      // Generate values that are variations of the systotal
      const baseValue = item.systotal;
      const variation = (Math.random() - 0.5) * 0.2; // ±10% variation
      dynamicData[field] = Math.round(baseValue * (1 + variation));
    });

    return dynamicData;
  });
};

const useBangCanDoiKeToan = (searchParams: any): UseBangCanDoiKeToanReturn => {
  const [data, setData] = useState<BangCanDoiKeToanItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BalanceSheetMultiPeriodsSchema) => {
    setIsLoading(true);
    setError(null);

    try {
      // Generate mock data based on form parameters
      const mockData = generateMockData(searchParams);

      const response = await api.get<BangCanDoiKeToanResponse>(
        '/tai-chinh/phan-tich-nhieu-ky-nhieu-doi-tuong/bang-can-doi-ke-toan-cho-nhieu-ky/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    searchParams,
    fetchData,
    refreshData
  };
};

export default useBangCanDoiKeToan;
