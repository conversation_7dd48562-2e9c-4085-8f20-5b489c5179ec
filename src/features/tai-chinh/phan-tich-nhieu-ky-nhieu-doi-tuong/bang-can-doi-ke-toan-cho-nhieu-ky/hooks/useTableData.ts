import { useMemo } from 'react';
import type { BangCanDoiKeToanItem } from '@/types/schemas/bang-can-doi-ke-toan-cho-nhieu-ky.type';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { generateColumns } from '../utils/generate-cols-utils';

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
}

export function useTableData(data: BangCanDoiKeToanItem[], formData: any): UseTableDataReturn {
  const tables = useMemo(() => {
    const columns = generateColumns(formData);

    const tableData: TableData[] = [
      {
        name: '',
        columns,
        rows: data.map(item => ({
          id: item.id,
          ma_so: item.ma_so,
          chi_tieu: item.chi_tieu,
          systotal: item.systotal,
          ...Object.keys(item)
            .filter(key => key.startsWith('period_'))
            .reduce((acc, key) => {
              acc[key] = item[key];
              return acc;
            }, {} as any)
        }))
      }
    ];

    return tableData;
  }, [data, formData]);

  const handleRowClick = (params: any) => {};

  return {
    tables,
    handleRowClick
  };
}
