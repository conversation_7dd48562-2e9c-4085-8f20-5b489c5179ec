import { REPORT_TEMPLATE_OPTIONS, CURRENCY_TEMPLATE_OPTIONS } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

export default function DetailTab() {
  return (
    <div className='h-40 space-y-3 overflow-y-auto p-4'>
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Chọn mẫu báo cáo:</Label>
        <FormField
          className='w-32 min-w-[300px]'
          label=''
          name='id_maubc'
          type='select'
          options={REPORT_TEMPLATE_OPTIONS}
        />
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Mẫu báo cáo:</Label>
        <FormField
          className='w-32 min-w-[300px]'
          type='select'
          label=''
          name='mau_bc'
          options={CURRENCY_TEMPLATE_OPTIONS}
        />
      </div>
    </div>
  );
}
