import { FormField } from '@/components/custom/arito/form/form-field';
import { TIME_TYPE_OPTIONS } from '@/types/schemas';
import { Label } from '@/components/ui/label';

export default function BasicInfoTab() {
  return (
    <div className='space-y-3 p-4'>
      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Kỳ</Label>
        <FormField className='w-20 min-w-[200px]' type='number' name='ky' />
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Năm</Label>
        <FormField className='w-20 min-w-[200px]' type='number' name='nam' />
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Loại thời gian</Label>
        <FormField className='w-20 min-w-[200px]' type='select' name='loai_ky' options={TIME_TYPE_OPTIONS} />
      </div>

      <div className='flex items-center'>
        <Label className='w-40 min-w-40'>Số kỳ</Label>
        <FormField className='w-20 min-w-[200px]' type='number' name='so_ky' />
      </div>
    </div>
  );
}
