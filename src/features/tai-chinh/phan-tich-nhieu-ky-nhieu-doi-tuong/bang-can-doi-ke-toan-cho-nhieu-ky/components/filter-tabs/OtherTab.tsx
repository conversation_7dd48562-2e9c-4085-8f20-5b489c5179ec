import { useState } from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { ANALYSIS_TEMPLATE_OPTIONS } from '@/types/schemas';
import { Label } from '@/components/ui/label';

export default function OtherTab() {
  const [saveAnalysisTemplateDialogOpen, setSaveAnalysisTemplateDialogOpen] = useState(false);

  const handleSaveAnalysisTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving analysis template:', data);
    setSaveAnalysisTemplateDialogOpen(false);
  };

  return (
    <div className='h-40 space-y-3 overflow-y-auto p-4'>
      <div className='flex items-center gap-2'>
        <Label className='w-40 min-w-40'>Mẫu phân tích DL:</Label>
        <FormField
          className='w-32 min-w-[300px]'
          type='select'
          label=''
          name='data_analysis_struct'
          options={ANALYSIS_TEMPLATE_OPTIONS}
        />
        <div className='h-9 w-9 flex-shrink-0'>
          {/* Fixed size container */}
          <RadixHoverDropdown
            iconNumber='...'
            items={[
              {
                value: 'save_new',
                label: 'Tạo mới mẫu phân tích',
                icon: 7,
                onClick: () => setSaveAnalysisTemplateDialogOpen(true)
              },
              {
                value: 'save_overwrite',
                label: 'Lưu đè vào mẫu đang chọn',
                icon: 75,
                onClick: () => console.log('Overwrite current template')
              },
              {
                value: 'delete',
                label: 'Xóa mẫu đang chọn',
                icon: 8,
                onClick: () => console.log('Delete current template')
              }
            ]}
          />
        </div>
      </div>

      {/* Save Analysis Template Dialog */}
      <SaveTemplateDialog
        open={saveAnalysisTemplateDialogOpen}
        onClose={() => setSaveAnalysisTemplateDialogOpen(false)}
        onSave={handleSaveAnalysisTemplate}
        templateType='analysis'
      />
    </div>
  );
}
