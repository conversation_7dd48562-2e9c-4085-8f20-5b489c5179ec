'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@mui/material';
import { z } from 'zod';
import { AritoHeaderTabs, BottomBar } from '@/components/custom/arito';
import { FormField } from '@/components/custom/arito/form/form-field';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { Label } from '@/components/ui/label';

interface SaveTemplateDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: SaveTemplateFormData) => void;
  templateType: 'filter' | 'analysis';
}

export interface SaveTemplateFormData {
  templateName: string;
  templateName2?: string;
  analysisTemplate?: string;
}

const saveTemplateSchema = z.object({
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  templateName2: z.string().optional(),
  analysisTemplate: z.string().optional()
});

const SaveTemplateDialog: React.FC<SaveTemplateDialogProps> = ({ open, onClose, onSave, templateType }) => {
  const [selectedField, setSelectedField] = useState<string>('');

  const title = templateType === 'filter' ? 'Lưu mẫu báo cáo' : 'Lưu mới mẫu phân tích';

  return (
    <AritoDialog open={open} onClose={onClose} title={title} titleIcon={<AritoIcon icon={12} />} maxWidth='lg'>
      <div>
        <AritoForm
          mode='add'
          initialData={{}}
          onSubmit={onSave}
          bottomBar={<BottomBar mode='add' onClose={onClose} />}
          schema={saveTemplateSchema}
          hasAritoActionBar={false}
          tabs={
            <div className='w-full space-y-4'>
              <div className='grid w-full grid-cols-1 gap-4'>
                <div className='rounded-md bg-white p-4'>
                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Nhập tên mẫu mới:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName' label='' type='text' className='w-full' />
                    </div>
                  </div>

                  <div className='flex items-center'>
                    <Label className='w-40 min-w-40 font-medium text-gray-700'>Tên 2:</Label>
                    <div className='flex-1'>
                      <FormField name='templateName2' label='' type='text' className='w-full' />
                    </div>
                  </div>
                </div>

                {templateType === 'analysis' && (
                  <div className=''>
                    <div className='mb-4 border-b border-gray-200'>
                      <AritoHeaderTabs
                        tabs={[
                          {
                            id: 'analysis',
                            label: 'Mẫu phân tích',
                            component: <></>
                          }
                        ]}
                      />
                      <div className='mb-6 rounded-md bg-white p-4'>
                        <div className='grid grid-cols-3 gap-4'>
                          {/* Left Column */}
                          <div className='rounded-lg border bg-white p-4'>
                            <div className='mb-4 flex items-center gap-2'>
                              <div className='h-4 w-4'>
                                <AritoIcon icon={869} />
                              </div>
                              <span className='text-sm font-medium'>Cột phân tích</span>
                            </div>
                            <div className='space-y-2'></div>
                          </div>

                          {/* You can add more columns here as needed */}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          }
        />
      </div>
    </AritoDialog>
  );
};

export default SaveTemplateDialog;
