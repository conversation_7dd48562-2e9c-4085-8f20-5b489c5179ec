'use client';

import { useState } from 'react';
import {
  AritoDataTables,
  AritoDialog,
  AritoForm,
  AritoHeaderTabs,
  AritoIcon,
  BottomBar,
  LoadingOverlay
} from '@/components/custom/arito';
import { BasicInfoTab, DetailTab, OtherTab, ActionBar } from './components';
import { useBangCanDoiKeToan, useTableData } from './hooks';
import { searchSchema, initialValues } from './schema';

export default function BangCanDoiKeToanChoNhieuKy() {
  const [showForm, setShowForm] = useState<boolean>(true);
  const [showTable, setShowTable] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<any>(initialValues);

  // Data hook for fetching balance sheet data
  const { data, fetchData, refreshData, isLoading } = useBangCanDoiKeToan(searchParams);

  // Table data hook for generating table structure
  const { tables } = useTableData(data, searchParams);

  const handleFormSubmit = async (formData: any) => {
    setSearchParams(formData);
    await fetchData(formData);
    setShowForm(false);
    setShowTable(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleSearchClick = () => {
    setShowForm(true);
  };

  const handleRefreshClick = async () => {
    await refreshData();
  };

  const handleFixedColumnsClick = () => {};

  const handleExportDataClick = () => {};

  const handleEditPrintTemplateClick = () => {};

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <AritoDialog
        open={showForm}
        onClose={handleCloseForm}
        title='Bảng cân đối kế toán'
        maxWidth='md'
        titleIcon={<AritoIcon icon={12} />}
      >
        <AritoForm
          mode='search'
          hasAritoActionBar={false}
          schema={searchSchema}
          initialData={initialValues}
          onSubmit={handleFormSubmit}
          onClose={handleCloseForm}
          className='w-full'
          title='Bảng cân đối kế toán'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] min-w-[900px] overflow-y-auto'>
              <BasicInfoTab />

              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'detail',
                    label: 'Chi tiết',
                    component: <DetailTab />
                  },
                  {
                    id: 'other',
                    label: 'Khác',
                    component: <OtherTab />
                  }
                ]}
              />
            </div>
          }
          classNameBottomBar='relative flex gap-2 w-full'
          bottomBar={<BottomBar mode='search' onClose={handleCloseForm} />}
        />
      </AritoDialog>

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportClick={handleExportDataClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
          />
          <div className='w-full overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} />}
          </div>
        </>
      )}
    </div>
  );
}
