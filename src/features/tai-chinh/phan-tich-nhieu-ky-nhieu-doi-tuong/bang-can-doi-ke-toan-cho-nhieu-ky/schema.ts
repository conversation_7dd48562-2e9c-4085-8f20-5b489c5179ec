import { z } from 'zod';

export const searchSchema = z.object({
  ky: z.coerce.number().min(1, { message: '<PERSON><PERSON> phải lớn hơn 0' }),
  nam: z.coerce.number().min(1000, { message: 'Năm phải lớn hơn 1000' }),
  loai_ky: z.string(),
  so_ky: z.coerce.number().min(1, { message: 'Số kỳ phải lớn hơn 0' }),

  id_maubc: z.string().optional(),
  mau_bc: z.string().optional(),

  data_analysis_struct: z.string().optional()
});

// Type inference from schema
export type BalanceSheetMultiPeriodsSchema = z.infer<typeof searchSchema>;

// Initial values for the form
export const initialValues: BalanceSheetMultiPeriodsSchema = {
  ky: 4,
  nam: new Date().getFullYear(),
  loai_ky: 'month',
  so_ky: 4,
  id_maubc: 'balance_sheet_tt200',
  mau_bc: 'standard_currency',
  data_analysis_struct: 'balance_sheet_unit'
};
