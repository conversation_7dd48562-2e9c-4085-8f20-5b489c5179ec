import { GridColDef } from '@mui/x-data-grid';
import { formatDate, getLastDayOfMonth } from './date-utils';

interface FormData {
  numOfPeriods: number | string;
  period: number | string;
  year: number | string;
  typeOfTime: 'month' | 'quarter' | 'halfYear' | 'year';
}

export const calculatePeriodColumns = (formData: FormData): GridColDef[] => {
  const numOfPeriods = Number(formData.numOfPeriods);
  const period = Number(formData.period);
  const year = Number(formData.year);
  const { typeOfTime } = formData;

  if (isNaN(numOfPeriods) || isNaN(period) || isNaN(year)) {
    console.error('Invalid form data: numeric values expected', formData);
    return [];
  }

  const columns: GridColDef[] = [];
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1;

  switch (typeOfTime) {
    case 'month': {
      let startMonth = period;
      let startYear = year;

      for (let i = 0; i < numOfPeriods; i++) {
        let calcMonth = startMonth + i;
        let calcYear = startYear;

        while (calcMonth > 12) {
          calcMonth -= 12;
          calcYear++;
        }

        const lastDay = getLastDayOfMonth(calcYear, calcMonth - 1);
        const headerText = formatDate(lastDay, calcMonth, calcYear);

        columns.push({
          field: `period_${i + 1}`,
          headerName: headerText,
          width: 150,
          type: 'number',
          renderCell: (params: any) => {
            return new Intl.NumberFormat('vi-VN').format(params.row[`period_${i + 1}`] || 0);
          }
        });
      }
      break;
    }

    case 'quarter': {
      // Q1: Jan-Mar (ends 3)
      // Q2: Apr-Jun (ends 6)
      // Q3: Jul-Sep (ends 9)
      // Q4: Oct-Dec (ends 12)
      const quarterEndMonths = [3, 6, 9, 12];

      // Get current quarter based on current month
      const currentQuarter = Math.ceil(currentMonth / 3);

      let calcQuarter = currentQuarter;
      let calcYear = year;

      for (let i = 0; i < numOfPeriods; i++) {
        const endMonth = quarterEndMonths[(calcQuarter - 1) % 4];

        const lastDay = getLastDayOfMonth(calcYear, endMonth - 1);
        const headerText = formatDate(lastDay, endMonth, calcYear);

        columns.push({
          field: `period_${i + 1}`,
          headerName: headerText,
          width: 150,
          type: 'number',
          renderCell: (params: any) => {
            return new Intl.NumberFormat('vi-VN').format(params.row[`period_${i + 1}`] || 0);
          }
        });

        // Move to next quarter
        calcQuarter++;
        if (calcQuarter > 4) {
          calcQuarter = 1;
          calcYear++;
        }
      }
      break;
    }

    case 'halfYear': {
      // If we're in Apr-Sep, we're in the period ending in Sep
      // If we're in Oct-Mar, we're in the period ending in Mar
      const currentHalf = currentMonth >= 4 && currentMonth <= 9 ? 2 : 1;

      // Start with the current year
      let calcYear = year;

      for (let i = 0; i < numOfPeriods; i++) {
        // For first period (i=0), use current half
        // For subsequent periods, alternate between halves
        const isFirstHalf = (currentHalf + i) % 2 === 1;
        const endMonth = isFirstHalf ? 3 : 9;

        // If it's ending in March, use next year
        const endYear = endMonth === 3 ? calcYear + 1 : calcYear;

        const lastDay = getLastDayOfMonth(endYear, endMonth - 1);
        const headerText = formatDate(lastDay, endMonth, endYear);

        columns.push({
          field: `period_${i + 1}`,
          headerName: headerText,
          width: 150,
          type: 'number',
          renderCell: (params: any) => {
            return new Intl.NumberFormat('vi-VN').format(params.row[`period_${i + 1}`] || 0);
          }
        });

        // Move to next year if we just processed a March ending period
        if (endMonth === 3) {
          calcYear++;
        }
      }
      break;
    }

    case 'year': {
      const startMonth = currentMonth - 1;

      for (let i = 0; i < numOfPeriods; i++) {
        const calcYear = year + i;
        const lastDay = getLastDayOfMonth(calcYear, startMonth - 1);
        const headerText = formatDate(lastDay, startMonth, calcYear);

        columns.push({
          field: `period_${i + 1}`,
          headerName: headerText,
          width: 150,
          type: 'number',
          renderCell: (params: any) => {
            return new Intl.NumberFormat('vi-VN').format(params.row[`period_${i + 1}`] || 0);
          }
        });
      }
      break;
    }

    default: {
      for (let i = 0; i < numOfPeriods; i++) {
        let calcMonth = period + i;
        let calcYear = year;

        while (calcMonth > 12) {
          calcMonth -= 12;
          calcYear++;
        }

        const lastDay = getLastDayOfMonth(calcYear, calcMonth - 1);
        const headerText = formatDate(lastDay, calcMonth, calcYear);

        columns.push({
          field: `period_${i + 1}`,
          headerName: headerText,
          width: 150,
          type: 'number',
          renderCell: (params: any) => {
            return new Intl.NumberFormat('vi-VN').format(params.row[`period_${i + 1}`] || 0);
          }
        });
      }
    }
  }

  return columns;
};
