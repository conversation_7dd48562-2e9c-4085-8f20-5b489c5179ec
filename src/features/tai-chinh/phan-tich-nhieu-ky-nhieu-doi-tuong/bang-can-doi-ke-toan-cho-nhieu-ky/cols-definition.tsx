import { GridColDef } from '@mui/x-data-grid';

// Basic columns that are always present
export const balanceSheetMultiPeriodsColumns: GridColDef[] = [
  { field: 'ma_so', headerName: 'Mã số', width: 150 },
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 250 },
  {
    field: 'systotal',
    headerName: 'Tổng cộng',
    width: 150,
    type: 'number',
    renderCell: (params: any) => {
      return new Intl.NumberFormat('vi-VN').format(params.row.systotal || 0);
    }
  }
];

// Available fields for configuration
export const availableFields = ['Stt', 'Cột xay', 'Tên cột xay', 'Mã số', 'Chi tiêu', 'Thuyết minh', 'Tiền nt', 'Tiền'];
