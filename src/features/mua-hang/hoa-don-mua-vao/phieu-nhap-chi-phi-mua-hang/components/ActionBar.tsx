import { Eye, FileText, Pencil, Plus, Printer, Search, Trash } from 'lucide-react';
import { useState } from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoMenuButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  onShowSearchForm: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
}

export function ActionBar({
  className,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onShowSearchForm,
  onViewClick,
  isEditDisabled = true,
  isViewDisabled = true
}: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu nhập chi phí mua hàng</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
      <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
      <AritoMenuButton
        title='In ấn'
        icon={Printer}
        items={[
          {
            title: 'Phiếu nhập chi phí mua hàng',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Chứng từ hạch toán',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (ngoại tệ)',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (song ngữ)',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Chứng từ hạch toán (song ngữ, ngoại tệ)',
            icon: <AritoIcon icon={20} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onShowSearchForm} />
      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
}
