import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import { AritoMenuActionButton } from './arito-menu-action-custom';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Hóa đơn mua hàng trong nước</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
    <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={onSearchClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: Printer,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Tải mẫu Excel',
          icon: FileSpreadsheet,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: FileUp,
          onClick: () => {},
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
