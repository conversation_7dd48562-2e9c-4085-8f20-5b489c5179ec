import { Check, Plus, RefreshCw, Table } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface Props {
  onSearch: () => void;
  onRefresh: () => void;
  onPinColumns: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export function ActionBar({ className, onSearch, onRefresh, onPinColumns, isEditDisabled = true }: Props) {
  return (
    <AritoActionBar titleComponent={<h1 className='relative text-xl font-bold'>Tất toán hoá đơn</h1>}>
      <>
        <AritoActionButton title='Tất toán' icon={Check} variant='primary' />
        <AritoActionButton title='Tìm kiếm' icon={Plus} onClick={onSearch} variant='primary' shortcut='Alt + N' />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} />
        <AritoActionButton title='Cố định cột' icon={Table} onClick={() => {}} />
      </>
    </AritoActionBar>
  );
}
