import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Hoá đơn dịch vụ trả lại nhà cung cấp</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton
      title='Sửa'
      icon={Pencil}
      onClick={onEditClick}
      // disabled={isEditDisabled}
    />
    <AritoActionButton
      title='Xóa'
      icon={Trash}
      onClick={onDeleteClick}
      // disabled={isDeleteDisabled}
    />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={onSearchClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: Printer,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);
