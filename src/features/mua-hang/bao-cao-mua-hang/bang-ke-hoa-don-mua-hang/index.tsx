'use client';

import React from 'react';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { useDialogState, useTableData, useActionHandlers } from './hooks';
import { ActionBar, InitialSearchDialog } from './components';

export default function BangKeHoaDonMuaHangPage() {
  const {
    initialSearchDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick
  } = useDialogState();

  const { tables, handleRowClick, refreshData, isLoading } = useTableData(searchParams);

  const { handleRefreshClick, handleFixedColumnsClick, handleExportDataClick, handlePrintClick } =
    useActionHandlers(refreshData);

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {/* Initial Search Dialog */}
      <InitialSearchDialog
        open={initialSearchDialogOpen}
        onClose={handleInitialSearchClose}
        onSearch={handleInitialSearch}
      />

      {/* Main Content */}
      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshClick}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onPrintClick={handlePrintClick}
            onEditPrintTemplateClick={handleEditPrintTemplateClick}
            fromDate={searchParams?.ngay_ct1}
            toDate={searchParams?.ngay_ct2}
          />
          <div className='w-full overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
