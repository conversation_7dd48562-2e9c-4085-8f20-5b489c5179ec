import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.coerce.date(),
  ngay_ct2: z.coerce.date(),
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),
  loai_du_lieu: z.string().optional(),

  ma_kh: z.string().optional(),
  nh_kh1: z.string().optional(),
  nh_kh2: z.string().optional(),
  nh_kh3: z.string().optional(),
  rg_code: z.string().optional(),
  ma_vt: z.string().optional(),
  ma_lvt: z.string().optional(),
  ton_kho_yn: z.boolean().optional(),
  nh_vt1: z.string().optional(),
  nh_vt2: z.string().optional(),
  nh_vt3: z.string().optional(),
  ma_kho: z.string().optional(),
  ma_unit: z.string().optional(),
  mau_bc: z.number().optional(),

  ma_bp: z.string().optional(),
  ma_vv: z.string().optional(),
  ma_hd: z.string().optional(),
  ma_dtt: z.string().optional(),
  ma_ku: z.string().optional(),
  ma_phi: z.string().optional(),
  ma_sp: z.string().optional(),
  ma_lsx: z.string().optional(),
  ma_cp0: z.string().optional(),

  ma_gd: z.string().optional(),
  tk_vt: z.string().optional(),
  tk_dt: z.string().optional(),
  tk_gv: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  dien_giai: z.string().optional(),
  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialValues: SearchFormValues = {
  ngay_ct1: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct2: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  so_ct1: '',
  so_ct2: '',
  loai_du_lieu: '5',

  ma_kh: '',
  nh_kh1: '',
  nh_kh2: '',
  nh_kh3: '',
  rg_code: '',
  ma_vt: '',
  ma_lvt: '',
  ton_kho_yn: true,
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ma_kho: '',
  ma_unit: '',
  mau_bc: 20,

  ma_bp: '',
  ma_vv: '',
  ma_hd: '',
  ma_dtt: '',
  ma_ku: '',
  ma_phi: '',
  ma_sp: '',
  ma_lsx: '',
  ma_cp0: '',

  ma_gd: '',
  tk_vt: '',
  tk_dt: '',
  tk_gv: '',
  ma_lo: '',
  ma_vi_tri: '',
  dien_giai: '',
  report_filtering: '0',
  data_analysis_struct: ''
};
