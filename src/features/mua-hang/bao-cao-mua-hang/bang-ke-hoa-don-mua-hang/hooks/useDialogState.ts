import { useState } from 'react';
import { SearchFormValues, initialValues } from '../schema';

export interface PrintTemplateData {
  [key: string]: any;
}

export interface UseDialogStateReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  searchParams: SearchFormValues;
  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: SearchFormValues) => Promise<void>;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (data: PrintTemplateData) => void;
  setShowTable: (show: boolean) => void;
}

export function useDialogState(): UseDialogStateReturn {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>(initialValues);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = async (values: SearchFormValues) => {
    setSearchParams(values);
    setShowTable(true);
    setInitialSearchDialogOpen(false);

    // The actual API call will be handled by useBangKeHoaDonData hook
    // when searchParams change
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleEditPrintTemplateClick = () => {
    setEditPrintTemplateDialogOpen(true);
  };

  const handleClosePrintTemplateDialog = () => {
    setEditPrintTemplateDialogOpen(false);
  };

  const handleSavePrintTemplate = (_data: PrintTemplateData) => {
    setEditPrintTemplateDialogOpen(false);
  };

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    setShowTable
  };
}
