import { useState, useCallback, useEffect } from 'react';
import {
  BangKeHoaDonItem,
  BangKeHoaDonResponse,
  UseBangKeHoaDonReturn
} from '@/types/schemas/bang_ke_hoa_don_mua_hang.type';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

const generateMockData = (): BangKeHoaDonItem[] => {
  return [
    {
      id: '1',
      unit_id: 'DV001',
      ma_ct: 'MCT001',
      ngay_ct: '2024-01-15',
      so_ct: 'CT001',
      ma_kh: 'NCC001',
      ma_vt: 'VT001',
      ma_kho: 'KHO001',
      dvt: 'Thùng',
      he_so: 1,
      sl_nhap: 10,
      gia: 150000,
      tien_nhap: 1500000,
      ck: 75000,
      cp: 50000,
      thue: 150000,
      dien_giai: '<PERSON><PERSON> vật tư văn phòng',
      tk_vt: '156',
      tk_du: '331',
      so_ct0: 'HD001',
      ngay_ct0: '2024-01-15',
      ma_bp: 'BP001',
      ma_vv: 'VV001',
      ma_hd: 'HD001',
      ma_ku: 'KU001',
      ma_phi: 'PHI001',
      ma_sp: 'SP001',
      ma_lsx: 'LSX001',
      ma_dtt: 'DTT001',
      ma_cp0: 'CP001',
      ma_unit: 'UNIT001',
      ngay_ct3: '2024-01-15',
      so_ct3: 'CT3001',
      ten_kh: 'Công ty TNHH ABC',
      ten_vt: 'Giấy A4',
      tien0: 1425000,
      tong_tt: 1625000
    },
    {
      id: '2',
      unit_id: 'DV001',
      ma_ct: 'MCT002',
      ngay_ct: '2024-01-16',
      so_ct: 'CT002',
      ma_kh: 'NCC002',
      ma_vt: 'VT002',
      ma_kho: 'KHO002',
      dvt: 'Tấn',
      he_so: 1,
      sl_nhap: 5,
      gia: 25000000,
      tien_nhap: 125000000,
      ck: 2500000,
      cp: 1000000,
      thue: 12500000,
      dien_giai: 'Mua nguyên liệu sản xuất',
      tk_vt: '156',
      tk_du: '331',
      so_ct0: 'HD002',
      ngay_ct0: '2024-01-16',
      ma_bp: 'BP002',
      ma_vv: 'VV002',
      ma_hd: 'HD002',
      ma_ku: 'KU002',
      ma_phi: 'PHI002',
      ma_sp: 'SP002',
      ma_lsx: 'LSX002',
      ma_dtt: 'DTT002',
      ma_cp0: 'CP002',
      ma_unit: 'UNIT002',
      ngay_ct3: '2024-01-16',
      so_ct3: 'CT3002',
      ten_kh: 'Công ty CP XYZ',
      ten_vt: 'Thép tấm',
      tien0: 123500000,
      tong_tt: 136000000
    },
    {
      id: '3',
      unit_id: 'DV001',
      ma_ct: 'MCT003',
      ngay_ct: '2024-01-17',
      so_ct: 'CT003',
      ma_kh: 'NCC003',
      ma_vt: 'VT003',
      ma_kho: 'KHO003',
      dvt: 'Cái',
      he_so: 1,
      sl_nhap: 1,
      gia: 500000000,
      tien_nhap: 500000000,
      ck: 10000000,
      cp: 5000000,
      thue: 50000000,
      dien_giai: 'Mua thiết bị máy móc',
      tk_vt: '211',
      tk_du: '331',
      so_ct0: 'HD003',
      ngay_ct0: '2024-01-17',
      ma_bp: 'BP003',
      ma_vv: 'VV003',
      ma_hd: 'HD003',
      ma_ku: 'KU003',
      ma_phi: 'PHI003',
      ma_sp: 'SP003',
      ma_lsx: 'LSX003',
      ma_dtt: 'DTT003',
      ma_cp0: 'CP003',
      ma_unit: 'UNIT003',
      ngay_ct3: '2024-01-17',
      so_ct3: 'CT3003',
      ten_kh: 'Công ty TNHH DEF',
      ten_vt: 'Máy cắt laser',
      tien0: 495000000,
      tong_tt: 545000000
    },
    {
      id: '4',
      unit_id: 'DV002',
      ma_ct: 'MCT004',
      ngay_ct: '2024-01-18',
      so_ct: 'CT004',
      ma_kh: 'NCC004',
      ma_vt: 'DV001',
      ma_kho: '',
      dvt: 'Lần',
      he_so: 1,
      sl_nhap: 1,
      gia: 15000000,
      tien_nhap: 15000000,
      ck: 500000,
      cp: 200000,
      thue: 1500000,
      dien_giai: 'Mua dịch vụ bảo trì',
      tk_vt: '627',
      tk_du: '331',
      so_ct0: 'HD004',
      ngay_ct0: '2024-01-18',
      ma_bp: 'BP004',
      ma_vv: 'VV004',
      ma_hd: 'HD004',
      ma_ku: 'KU004',
      ma_phi: 'PHI004',
      ma_sp: 'SP004',
      ma_lsx: 'LSX004',
      ma_dtt: 'DTT004',
      ma_cp0: 'CP004',
      ma_unit: 'UNIT004',
      ngay_ct3: '2024-01-18',
      so_ct3: 'CT3004',
      ten_kh: 'Công ty CP GHI',
      ten_vt: 'Dịch vụ bảo trì máy móc',
      tien0: 14700000,
      tong_tt: 16200000
    },
    {
      id: '5',
      unit_id: 'DV002',
      ma_ct: 'MCT005',
      ngay_ct: '2024-01-19',
      so_ct: 'CT005',
      ma_kh: 'NCC005',
      ma_vt: 'VT004',
      ma_kho: 'KHO004',
      dvt: 'Lít',
      he_so: 1,
      sl_nhap: 100,
      gia: 50000,
      tien_nhap: 5000000,
      ck: 100000,
      cp: 50000,
      thue: 500000,
      dien_giai: 'Mua hóa chất sản xuất',
      tk_vt: '156',
      tk_du: '331',
      so_ct0: 'HD005',
      ngay_ct0: '2024-01-19',
      ma_bp: 'BP005',
      ma_vv: 'VV005',
      ma_hd: 'HD005',
      ma_ku: 'KU005',
      ma_phi: 'PHI005',
      ma_sp: 'SP005',
      ma_lsx: 'LSX005',
      ma_dtt: 'DTT005',
      ma_cp0: 'CP005',
      ma_unit: 'UNIT005',
      ngay_ct3: '2024-01-19',
      so_ct3: 'CT3005',
      ten_kh: 'Công ty TNHH JKL',
      ten_vt: 'Hóa chất tẩy rửa',
      tien0: 4950000,
      tong_tt: 5450000
    }
  ];
};

/**
 * Custom hook for managing Bang Ke Hoa Don Mua Hang data
 *
 * This hook provides functionality to fetch invoice purchase report data
 * with mock support for testing and development purposes.
 */
export function useBangKeHoaDonData(searchParams: SearchFormValues): UseBangKeHoaDonReturn {
  const [data, setData] = useState<BangKeHoaDonItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Use mock data directly for now
      const mockData = generateMockData();

      const response = await api.get<BangKeHoaDonResponse>('/mua-hang/bao-cao-mua-hang/bang-ke-hoa-don-mua-hang/', {
        mock: true,
        mockData,
        params: searchParams
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  // Auto-fetch data when searchParams change or on initial load
  useEffect(() => {
    fetchData(searchParams);
  }, [searchParams, fetchData]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
