import { useState } from 'react';
import {
  DoiTuong,
  Group,
  KhuVuc,
  VatTu,
  KhoHang,
  BoPhan,
  VuViec,
  HopDong,
  DotThanhToan,
  KheUoc,
  Phi,
  ChiPhi,
  <PERSON><PERSON>,
  <PERSON>,
  Vi<PERSON><PERSON>
} from '@/types/schemas';

export interface SearchDialogState {
  // Customer/Supplier related
  khachHang: DoiTuong | null;
  nhomKhachHang1: Group | null;
  nhomKhachHang2: Group | null;
  nhomKhachHang3: Group | null;
  khuVuc: KhuVuc | null;

  // Item related
  vatTu: VatTu | null;
  nhomVatTu1: Group | null;
  nhomVatTu2: Group | null;
  nhomVatTu3: Group | null;
  khoHang: KhoHang | null;

  // Object tab fields
  boPhan: BoPhan | null;
  vuViec: VuViec | null;
  hopDong: HopDong | null;
  dotThanhToan: DotThanhToan | null;
  kheUoc: KheUoc | null;
  phi: Phi | null;
  sanPham: VatTu | null;
  lenhSanXuat: any | null;
  chiPhi: ChiPhi | null;

  // Other tab fields
  giaoDich: any | null;
  taiKhoanVatTu: TaiKhoan | null;
  taiKhoanDoanhThu: TaiKhoan | null;
  taiKhoanGiaVon: TaiKhoan | null;
  lo: Lo | null;
  viTri: ViTri | null;
}

export interface SearchDialogActions {
  // Customer/Supplier related
  setKhachHang: (khachHang: DoiTuong) => void;
  setNhomKhachHang1: (nhomKhachHang1: Group) => void;
  setNhomKhachHang2: (nhomKhachHang2: Group) => void;
  setNhomKhachHang3: (nhomKhachHang3: Group) => void;
  setKhuVuc: (khuVuc: KhuVuc) => void;

  // Item related
  setVatTu: (vatTu: VatTu) => void;
  setNhomVatTu1: (nhomVatTu1: Group) => void;
  setNhomVatTu2: (nhomVatTu2: Group) => void;
  setNhomVatTu3: (nhomVatTu3: Group) => void;
  setKhoHang: (khoHang: KhoHang) => void;

  // Object tab fields
  setBoPhan: (boPhan: BoPhan) => void;
  setVuViec: (vuViec: VuViec) => void;
  setHopDong: (hopDong: HopDong) => void;
  setDotThanhToan: (dotThanhToan: DotThanhToan) => void;
  setKheUoc: (kheUoc: KheUoc) => void;
  setPhi: (phi: Phi) => void;
  setSanPham: (sanPham: VatTu) => void;
  setLenhSanXuat: (lenhSanXuat: any) => void;
  setChiPhi: (chiPhi: ChiPhi) => void;

  // Other tab fields
  setGiaoDich: (giaoDich: any) => void;
  setTaiKhoanVatTu: (taiKhoanVatTu: TaiKhoan) => void;
  setTaiKhoanDoanhThu: (taiKhoanDoanhThu: TaiKhoan) => void;
  setTaiKhoanGiaVon: (taiKhoanGiaVon: TaiKhoan) => void;
  setLo: (lo: Lo) => void;
  setViTri: (viTri: ViTri) => void;

  // Utility actions
  resetState: () => void;
  updateState: (updates: Partial<SearchDialogState>) => void;
}

export interface UseSearchFieldStatesReturn {
  state: SearchDialogState;
  actions: SearchDialogActions;
}

const initialState: SearchDialogState = {
  // Customer/Supplier related
  khachHang: null,
  nhomKhachHang1: null,
  nhomKhachHang2: null,
  nhomKhachHang3: null,
  khuVuc: null,

  // Item related
  vatTu: null,
  nhomVatTu1: null,
  nhomVatTu2: null,
  nhomVatTu3: null,
  khoHang: null,

  // Object tab fields
  boPhan: null,
  vuViec: null,
  hopDong: null,
  dotThanhToan: null,
  kheUoc: null,
  phi: null,
  sanPham: null,
  lenhSanXuat: null,
  chiPhi: null,

  // Other tab fields
  giaoDich: null,
  taiKhoanVatTu: null,
  taiKhoanDoanhThu: null,
  taiKhoanGiaVon: null,
  lo: null,
  viTri: null
};

export function useSearchFieldStates(initialValues?: Partial<SearchDialogState>): UseSearchFieldStatesReturn {
  const [state, setState] = useState<SearchDialogState>({
    ...initialState,
    ...initialValues
  });

  const actions: SearchDialogActions = {
    // Customer/Supplier related
    setKhachHang: (khachHang: DoiTuong) => {
      setState(prev => ({ ...prev, khachHang }));
    },

    setNhomKhachHang1: (nhomKhachHang1: Group) => {
      setState(prev => ({ ...prev, nhomKhachHang1 }));
    },

    setNhomKhachHang2: (nhomKhachHang2: Group) => {
      setState(prev => ({ ...prev, nhomKhachHang2 }));
    },

    setNhomKhachHang3: (nhomKhachHang3: Group) => {
      setState(prev => ({ ...prev, nhomKhachHang3 }));
    },

    setKhuVuc: (khuVuc: KhuVuc) => {
      setState(prev => ({ ...prev, khuVuc }));
    },

    // Item related
    setVatTu: (vatTu: VatTu) => {
      setState(prev => ({ ...prev, vatTu }));
    },

    setNhomVatTu1: (nhomVatTu1: Group) => {
      setState(prev => ({ ...prev, nhomVatTu1 }));
    },

    setNhomVatTu2: (nhomVatTu2: Group) => {
      setState(prev => ({ ...prev, nhomVatTu2 }));
    },

    setNhomVatTu3: (nhomVatTu3: Group) => {
      setState(prev => ({ ...prev, nhomVatTu3 }));
    },

    setKhoHang: (khoHang: KhoHang) => {
      setState(prev => ({ ...prev, khoHang }));
    },

    // Object tab fields
    setBoPhan: (boPhan: BoPhan) => {
      setState(prev => ({ ...prev, boPhan }));
    },

    setVuViec: (vuViec: VuViec) => {
      setState(prev => ({ ...prev, vuViec }));
    },

    setHopDong: (hopDong: HopDong) => {
      setState(prev => ({ ...prev, hopDong }));
    },

    setDotThanhToan: (dotThanhToan: DotThanhToan) => {
      setState(prev => ({ ...prev, dotThanhToan }));
    },

    setKheUoc: (kheUoc: KheUoc) => {
      setState(prev => ({ ...prev, kheUoc }));
    },

    setPhi: (phi: Phi) => {
      setState(prev => ({ ...prev, phi }));
    },

    setSanPham: (sanPham: VatTu) => {
      setState(prev => ({ ...prev, sanPham }));
    },

    setLenhSanXuat: (lenhSanXuat: any) => {
      setState(prev => ({ ...prev, lenhSanXuat }));
    },

    setChiPhi: (chiPhi: ChiPhi) => {
      setState(prev => ({ ...prev, chiPhi }));
    },

    // Other tab fields
    setGiaoDich: (giaoDich: any) => {
      setState(prev => ({ ...prev, giaoDich }));
    },

    setTaiKhoanVatTu: (taiKhoanVatTu: TaiKhoan) => {
      setState(prev => ({ ...prev, taiKhoanVatTu }));
    },

    setTaiKhoanDoanhThu: (taiKhoanDoanhThu: TaiKhoan) => {
      setState(prev => ({ ...prev, taiKhoanDoanhThu }));
    },

    setTaiKhoanGiaVon: (taiKhoanGiaVon: TaiKhoan) => {
      setState(prev => ({ ...prev, taiKhoanGiaVon }));
    },

    setLo: (lo: Lo) => {
      setState(prev => ({ ...prev, lo }));
    },

    setViTri: (viTri: ViTri) => {
      setState(prev => ({ ...prev, viTri }));
    },

    // Utility actions
    resetState: () => {
      setState(initialState);
    },

    updateState: (updates: Partial<SearchDialogState>) => {
      setState(prev => ({ ...prev, ...updates }));
    }
  };

  return { state, actions };
}
