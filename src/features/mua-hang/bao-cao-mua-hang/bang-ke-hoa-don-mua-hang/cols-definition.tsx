import { GridColDef } from '@mui/x-data-grid';

export const invoiceReportColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  {
    field: 'unit_id',
    headerName: 'Đơn vị',
    width: 100
  },
  {
    field: 'ngay_ct',
    headerName: 'Ngày c/từ',
    width: 110,
    type: 'date',
    valueGetter: (value: string) => {
      return value ? new Date(value) : null;
    }
  },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120
  },
  {
    field: 'ngay_ct0',
    headerName: 'Ngày nhập',
    width: 110,
    type: 'date',
    valueGetter: (value: string) => {
      return value ? new Date(value) : null;
    }
  },
  {
    field: 'so_ct0',
    headerName: 'Số c/từ nhập',
    width: 120
  },
  {
    field: 'ngay_ct3',
    headerName: '<PERSON><PERSON><PERSON> hóa đơn',
    width: 120,
    type: 'date',
    valueGetter: (value: string) => {
      return value ? new Date(value) : null;
    }
  },
  {
    field: 'so_ct3',
    headerName: 'Số hóa đơn',
    width: 120
  },
  {
    field: 'ma_kh',
    headerName: 'Mã NCC',
    width: 100
  },
  {
    field: 'ten_kh',
    headerName: 'Tên NCC',
    width: 200
  },
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200
  },
  {
    field: 'ma_vt',
    headerName: 'Mã vật tư',
    width: 120
  },
  {
    field: 'ten_vt',
    headerName: 'Tên vật tư',
    width: 200
  },
  {
    field: 'dvt',
    headerName: 'ĐVT',
    width: 80
  },
  {
    field: 'sl_nhap',
    headerName: 'Số lượng',
    width: 100,
    type: 'number'
  },
  {
    field: 'gia',
    headerName: 'Giá',
    width: 120,
    type: 'number'
  },
  {
    field: 'tien0',
    headerName: 'Tiền',
    width: 120,
    type: 'number'
  },
  {
    field: 'ck',
    headerName: 'Chiết khấu',
    width: 120,
    type: 'number'
  },
  {
    field: 'tien_nhap',
    headerName: 'Tiền nhập',
    width: 120,
    type: 'number'
  },
  {
    field: 'thue',
    headerName: 'Thuế',
    width: 100,
    type: 'number'
  },
  {
    field: 'ma_ct',
    headerName: 'Mã tính chất',
    width: 120
  },
  {
    field: 'cp',
    headerName: 'Chi phí',
    width: 120,
    type: 'number'
  },
  {
    field: 'tong_tt',
    headerName: 'Tổng thanh toán',
    width: 140,
    type: 'number'
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100
  },
  {
    field: 'tk_vt',
    headerName: 'TK vật tư',
    width: 100
  },
  {
    field: 'tk_du',
    headerName: 'TK công nợ',
    width: 120
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 130
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 100
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 130
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 130
  },
  {
    field: 'ma_ct',
    headerName: 'Mã c/từ',
    width: 120
  }
];
