import { <PERSON><PERSON>, <PERSON>er, RefreshCw, Search, Sheet } from 'lucide-react';
import React from 'react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';

interface Props {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
  onPrintClick: () => void;
  onEditPrintTemplateClick: () => void;
  fromDate?: string | Date;
  toDate?: string | Date;
  className?: string;
}

export function ActionBar({
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onPrintClick,
  onEditPrintTemplateClick,
  fromDate,
  toDate,
  className
}: Props) {
  // Create date range display
  const getDateRangeDisplay = () => {
    if (!fromDate || !toDate) return '';

    const formattedFromDate = fromDate instanceof Date ? fromDate.toISOString().split('T')[0] : fromDate;
    const formattedToDate = toDate instanceof Date ? toDate.toISOString().split('T')[0] : toDate;

    if (formattedFromDate && formattedToDate) {
      return `Từ ngày ${formattedFromDate} đến ngày ${formattedToDate}`;
    }

    return '';
  };

  const dateRangeText = getDateRangeDisplay();
  return (
    <AritoActionBar
      className={className}
      titleComponent={
        <div className='flex flex-col'>
          <h1 className='text-xl font-bold'>Bảng kê hóa đơn mua hàng</h1>
          {dateRangeText && (
            <p className='mt-1 text-sm text-gray-600'>
              <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
              {dateRangeText}
            </p>
          )}
        </div>
      }
    >
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='primary' />
      <AritoActionButton title='Làm mới' variant='destructive' icon={RefreshCw} onClick={onRefreshClick} />
      <AritoActionButton title='Cố định cột' icon={Pin} onClick={onFixedColumnsClick} />
      <AritoActionButton title='Xuất dữ liệu' icon={Sheet} onClick={onExportDataClick} />
      <AritoActionButton title='In' icon={Printer} onClick={onPrintClick} />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Sửa mẫu in',
            icon: <AritoIcon icon={864} />,
            onClick: onEditPrintTemplateClick,
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
}
