import React from 'react';
import { AritoHeaderTabs, AritoForm, AritoDialog, AritoIcon, BottomBar } from '@/components/custom/arito';
import { searchSchema, initialValues, SearchFormValues } from '../schema';
import { BasicInfoTab, GeneralTab, ObjectTab, OtherTab } from './tabs';
import { useSearchFieldStates } from '../hooks';
import { FormMode } from '@/types/form';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => Promise<void>;
}

export default function InitialSearchDialog({ open, onClose, onSearch }: InitialSearchDialogProps) {
  const searchFieldStates = useSearchFieldStates();
  const { state } = searchFieldStates;

  const handleSubmit = async (data: SearchFormValues) => {
    // Map search state to form values before submitting
    const combinedData: SearchFormValues = {
      ...data,
      // Customer/Supplier related
      ma_kh: state.khachHang?.uuid || '',
      nh_kh1: state.nhomKhachHang1?.uuid || '',
      nh_kh2: state.nhomKhachHang2?.uuid || '',
      nh_kh3: state.nhomKhachHang3?.uuid || '',
      rg_code: state.khuVuc?.uuid || '',

      // Item related
      ma_vt: state.vatTu?.uuid || '',
      nh_vt1: state.nhomVatTu1?.uuid || '',
      nh_vt2: state.nhomVatTu2?.uuid || '',
      nh_vt3: state.nhomVatTu3?.uuid || '',
      ma_kho: state.khoHang?.uuid || '',

      // Object tab fields
      ma_bp: state.boPhan?.uuid || '',
      ma_vv: state.vuViec?.uuid || '',
      ma_hd: state.hopDong?.uuid || '',
      ma_dtt: state.dotThanhToan?.uuid || '',
      ma_ku: state.kheUoc?.uuid || '',
      ma_phi: state.phi?.uuid || '',
      ma_sp: state.sanPham?.uuid || '',
      ma_lsx: state.lenhSanXuat?.uuid || '',
      ma_cp0: state.chiPhi?.uuid || '',

      // Other tab fields
      ma_gd: state.giaoDich?.uuid || '',
      tk_vt: state.taiKhoanVatTu?.uuid || '',
      tk_dt: state.taiKhoanDoanhThu?.uuid || '',
      tk_gv: state.taiKhoanGiaVon?.uuid || '',
      ma_lo: state.lo?.uuid || '',
      ma_vi_tri: state.viTri?.uuid || ''
    };

    await onSearch(combinedData);
  };

  const tabs = [
    {
      id: 'general',
      label: 'Thông tin chung',
      component: <GeneralTab formMode={'add' as FormMode} searchState={searchFieldStates} />
    },
    {
      id: 'object',
      label: 'Đối tượng',
      component: <ObjectTab formMode={'add' as FormMode} searchState={searchFieldStates} />
    },
    {
      id: 'other',
      label: 'Khác',
      component: <OtherTab formMode={'add' as FormMode} searchState={searchFieldStates} />
    }
  ];

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Bảng kê hóa đơn mua hàng'
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='lg'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<SearchFormValues>
            schema={searchSchema}
            initialData={initialValues}
            onSubmit={handleSubmit}
            className='!static !w-full'
            hasAritoActionBar={false}
            headerFields={
              <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
                <BasicInfoTab />

                <AritoHeaderTabs tabs={tabs} />
              </div>
            }
            classNameBottomBar='relative w-full flex justify-end gap-2 px-2'
            bottomBar={<BottomBar mode='search' onClose={onClose} />}
          />
        </div>
      </div>
    </AritoDialog>
  );
}
