import React from 'react';
import DocumentNumberRange from '@/components/custom/arito/form/search-fields/DocumentNumberRangeField';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { Label } from '@/components/ui/label';

const BasicInfoTab: React.FC = () => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Date Range Fields */}
        <div className='flex w-[61%] items-center'>
          <Label className='w-40 min-w-40'>Ng<PERSON>y nhập từ/đến:</Label>
          <div>
            <AritoFormDateRangeDropdown fromDateName='ngay_ct1' toDateName='ngay_ct2' />
          </div>
        </div>

        {/* <PERSON><PERSON> chứng từ từ/đến */}
        <div className='flex items-center'>
          <DocumentNumberRange fromName='so_ct1' toName='so_ct2' />
        </div>

        {/* Loại dữ liệu */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại dữ liệu:</Label>
          <div className='w-[57.5%]'>
            <RadioButton
              name='loai_du_lieu'
              options={[
                { value: '1', label: 'Hàng hóa' },
                { value: '2', label: 'Dịch vụ' },
                { value: '5', label: 'Hàng hóa và dịch vụ' }
              ]}
              orientation='horizontal'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
