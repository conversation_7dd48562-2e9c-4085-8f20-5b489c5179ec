import React from 'react';
import {
  giaoDichSearchColumns,
  accountSearchColumns,
  loSearchColumns,
  locationSearchColumns
} from '@/constants/search-columns';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchDialogState, SearchDialogActions } from '../../hooks';
import { TaiKhoan, Lo, ViTri } from '@/types/schemas';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface OtherTabProps {
  formMode?: FormMode;
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

const OtherTab: React.FC<OtherTabProps> = ({ searchState: { state, actions } }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã giao dịch */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã giao dịch:</Label>
          <div className='w-[57.5%]'>
            <SearchField<any>
              type='text'
              searchEndpoint={`/`}
              searchColumns={giaoDichSearchColumns}
              dialogTitle='Danh mục giao dịch'
              columnDisplay={`ma_giao_dich`}
              displayRelatedField={`ten_giao_dich`}
              value={state.giaoDich?.ma_giao_dich || ''}
              relatedFieldValue={state.giaoDich?.ten_giao_dich || ''}
              onRowSelection={actions.setGiaoDich}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Tài khoản vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField<TaiKhoan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay={`code`}
              displayRelatedField={`name`}
              value={state.taiKhoanVatTu?.code || ''}
              relatedFieldValue={state.taiKhoanVatTu?.name || ''}
              onRowSelection={actions.setTaiKhoanVatTu}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Tài khoản doanh thu */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản doanh thu:</Label>
          <div className='w-[57.5%]'>
            <SearchField<TaiKhoan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay={`code`}
              displayRelatedField={`name`}
              value={state.taiKhoanDoanhThu?.code || ''}
              relatedFieldValue={state.taiKhoanDoanhThu?.name || ''}
              onRowSelection={actions.setTaiKhoanDoanhThu}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Tài khoản giá vốn */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Tài khoản giá vốn:</Label>
          <div className='w-[57.5%]'>
            <SearchField<TaiKhoan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
              searchColumns={accountSearchColumns}
              dialogTitle='Danh mục tài khoản'
              columnDisplay={`code`}
              displayRelatedField={`name`}
              value={state.taiKhoanGiaVon?.code || ''}
              relatedFieldValue={state.taiKhoanGiaVon?.name || ''}
              onRowSelection={actions.setTaiKhoanGiaVon}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã lô */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã lô:</Label>
          <div className='w-[57.5%]'>
            <SearchField<Lo>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LO}/`}
              searchColumns={loSearchColumns}
              dialogTitle='Danh mục lô'
              columnDisplay={`ma_lo`}
              displayRelatedField={`ten_lo`}
              value={state.lo?.ma_lo || ''}
              relatedFieldValue={state.lo?.ten_lo || ''}
              onRowSelection={actions.setLo}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vị trí */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vị trí:</Label>
          <div className='w-[57.5%]'>
            <SearchField<ViTri>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VI_TRI}/`}
              searchColumns={locationSearchColumns}
              dialogTitle='Danh mục vị trí'
              columnDisplay={`ma_vi_tri`}
              displayRelatedField={`ten_vi_tri`}
              value={state.viTri?.ma_vi_tri || ''}
              relatedFieldValue={state.viTri?.ten_vi_tri || ''}
              onRowSelection={actions.setViTri}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Diễn giải */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Diễn giải:</Label>
          <div className='w-[57.5%]'>
            <FormField type='text' name='dien_giai' className='w-full' />
          </div>
        </div>

        {/* Mẫu lọc báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='report_filtering'
              type='select'
              options={[{ value: '0', label: 'Người dùng tự lọc' }]}
              className='w-96'
            />

            <div className='flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => console.log('Save new filter template')
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>

        {/* Mẫu phân tích dữ liệu */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mẫu phân tích DL:</Label>
          <div className='flex items-center gap-1'>
            <FormField
              name='data_analysis_struct'
              type='select'
              options={[{ value: '0', label: 'Không phân tích' }]}
              className='w-96'
            />

            <div className='flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={790}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => console.log('Save new analysis template')
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Sửa mẫu đang chọn',
                    icon: 9,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtherTab;
