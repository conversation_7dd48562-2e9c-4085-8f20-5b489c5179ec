import React from 'react';
import {
  boPhanSearchColumns,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  lenhSanXuatSearchColumns,
  chiPhiSearchColumns
} from '@/constants/search-columns';
import { BoPhan, VuViec, HopDong, DotThanhToan, KheUoc, Phi, VatTu, ChiPhi } from '@/types/schemas';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { SearchDialogState, SearchDialogActions } from '../../hooks';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface ObjectTabProps {
  formMode?: FormMode;
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

const ObjectTab: React.FC<ObjectTabProps> = ({ searchState: { state, actions } }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã bộ phận */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã bộ phận:</Label>
          <div className='w-[57.5%]'>
            <SearchField<BoPhan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.BO_PHAN}/`}
              searchColumns={boPhanSearchColumns}
              dialogTitle='Danh mục bộ phận'
              columnDisplay={`ma_bp`}
              displayRelatedField={`ten_bp`}
              value={state.boPhan?.ma_bp || ''}
              relatedFieldValue={state.boPhan?.ten_bp || ''}
              onRowSelection={actions.setBoPhan}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vụ việc */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vụ việc:</Label>
          <div className='w-[57.5%]'>
            <SearchField<VuViec>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
              searchColumns={vuViecSearchColumns}
              dialogTitle='Danh mục vụ việc'
              columnDisplay={`ma_vu_viec`}
              displayRelatedField={`ten_vu_viec`}
              value={state.vuViec?.ma_vu_viec || ''}
              relatedFieldValue={state.vuViec?.ten_vu_viec || ''}
              onRowSelection={actions.setVuViec}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã hợp đồng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã hợp đồng:</Label>
          <div className='w-[57.5%]'>
            <SearchField<HopDong>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
              searchColumns={hopDongSearchColumns}
              dialogTitle='Danh mục hợp đồng'
              columnDisplay={`ma_hd`}
              displayRelatedField={`ten_hd`}
              value={state.hopDong?.ma_hd || ''}
              relatedFieldValue={state.hopDong?.ten_hd || ''}
              onRowSelection={actions.setHopDong}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã đợt thanh toán */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã đợt thanh toán:</Label>
          <div className='w-[57.5%]'>
            <SearchField<DotThanhToan>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
              searchColumns={dotThanhToanSearchColumns}
              dialogTitle='Danh mục đợt thanh toán'
              columnDisplay={`ma_dtt`}
              displayRelatedField={`ten_dtt`}
              value={state.dotThanhToan?.ma_dtt || ''}
              relatedFieldValue={state.dotThanhToan?.ten_dtt || ''}
              onRowSelection={actions.setDotThanhToan}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã khế ước */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã khế ước:</Label>
          <div className='w-[57.5%]'>
            <SearchField<KheUoc>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
              searchColumns={kheUocSearchColumns}
              dialogTitle='Danh mục khế ước'
              columnDisplay={`ma_ku`}
              displayRelatedField={`ten_ku`}
              value={state.kheUoc?.ma_ku || ''}
              relatedFieldValue={state.kheUoc?.ten_ku || ''}
              onRowSelection={actions.setKheUoc}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã phí */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã phí:</Label>
          <div className='w-[57.5%]'>
            <SearchField<Phi>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.PHI}/`}
              searchColumns={phiSearchColumns}
              dialogTitle='Danh mục phí'
              columnDisplay={`ma_phi`}
              displayRelatedField={`ten_phi`}
              value={state.phi?.ma_phi || ''}
              relatedFieldValue={state.phi?.ten_phi || ''}
              onRowSelection={actions.setPhi}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã sản phẩm */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã sản phẩm:</Label>
          <div className='w-[57.5%]'>
            <SearchField<VatTu>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              dialogTitle='Danh mục sản phẩm'
              columnDisplay={`ma_vt`}
              displayRelatedField={`ten_vt`}
              value={state.sanPham?.ma_vt || ''}
              relatedFieldValue={state.sanPham?.ten_vt || ''}
              onRowSelection={actions.setSanPham}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Số lệnh */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Số lệnh:</Label>
          <div className='w-[57.5%]'>
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}/`}
              searchColumns={lenhSanXuatSearchColumns}
              dialogTitle='Danh mục lệnh sản xuất'
              columnDisplay={`ma_lsx`}
              displayRelatedField={`ten_lsx`}
              value={state.lenhSanXuat?.ma_lsx || ''}
              relatedFieldValue={state.lenhSanXuat?.ten_lsx || ''}
              onRowSelection={actions.setLenhSanXuat}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã chi phí */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã chi phí:</Label>
          <div className='w-[57.5%]'>
            <SearchField<ChiPhi>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.CHI_PHI}/`}
              searchColumns={chiPhiSearchColumns}
              dialogTitle='Danh mục chi phí'
              columnDisplay={`ma_cp`}
              displayRelatedField={`ten_cp`}
              value={state.chiPhi?.ma_cp || ''}
              relatedFieldValue={state.chiPhi?.ten_cp || ''}
              onRowSelection={actions.setChiPhi}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ObjectTab;
