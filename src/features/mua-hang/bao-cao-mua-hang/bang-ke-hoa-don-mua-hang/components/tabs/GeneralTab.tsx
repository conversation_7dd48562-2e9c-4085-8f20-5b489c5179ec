import React from 'react';
import {
  supplierSearchColumns,
  supplierGroupSearchColumns,
  regionSearchColumns,
  vatTuSearchColumns,
  nhomColumns,
  warehouseSearchColumns
} from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { DoiTuong, Group, KhuVuc, VatTu, KhoHang } from '@/types/schemas';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchDialogState, SearchDialogActions } from '../../hooks';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface GeneralTabProps {
  formMode?: FormMode;
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

const GeneralTab: React.FC<GeneralTabProps> = ({ searchState: { state, actions } }) => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-4 p-4'>
      <div className='flex flex-col space-y-3'>
        {/* Mã nhà cung cấp */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã nhà cung cấp:</Label>
          <div className='w-[57.5%]'>
            <SearchField<DoiTuong>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.NHA_CUNG_CAP}/`}
              searchColumns={supplierSearchColumns}
              dialogTitle='Danh mục nhà cung cấp'
              columnDisplay={`customer_code`}
              displayRelatedField={`customer_name`}
              value={state.khachHang?.customer_code || ''}
              relatedFieldValue={state.khachHang?.customer_name || ''}
              onRowSelection={actions.setKhachHang}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Nhóm nhà cung cấp - 3 field trong 1 hàng */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Nhóm NCC:</Label>
          <div className='flex w-[57.5%] gap-2'>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                searchColumns={supplierGroupSearchColumns}
                dialogTitle='Danh mục nhóm nhà cung cấp'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomKhachHang1?.ma_nhom || ''}
                relatedFieldValue={state.nhomKhachHang1?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomKhachHang1}
                className='w-full'
              />
            </div>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                searchColumns={supplierGroupSearchColumns}
                dialogTitle='Danh mục nhóm nhà cung cấp 2'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomKhachHang2?.ma_nhom || ''}
                relatedFieldValue={state.nhomKhachHang2?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomKhachHang2}
                className='w-full'
              />
            </div>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_NHA_CUNG_CAP}/`}
                searchColumns={supplierGroupSearchColumns}
                dialogTitle='Danh mục nhóm nhà cung cấp 3'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomKhachHang3?.ma_nhom || ''}
                relatedFieldValue={state.nhomKhachHang3?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomKhachHang3}
                className='w-full'
              />
            </div>
          </div>
        </div>

        {/* Khu vực */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Khu vực:</Label>
          <div className='w-[57.5%]'>
            <SearchField<KhuVuc>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHU_VUC}/`}
              searchColumns={regionSearchColumns}
              dialogTitle='Danh mục khu vực'
              columnDisplay={`rg_code`}
              displayRelatedField={`rgname`}
              value={state.khuVuc?.rg_code || ''}
              relatedFieldValue={state.khuVuc?.rgname || ''}
              onRowSelection={actions.setKhuVuc}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mã vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField<VatTu>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
              searchColumns={vatTuSearchColumns}
              dialogTitle='Danh mục vật tư'
              columnDisplay={`ma_vt`}
              displayRelatedField={`ten_vt`}
              value={state.vatTu?.ma_vt || ''}
              relatedFieldValue={state.vatTu?.ten_vt || ''}
              onRowSelection={actions.setVatTu}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Loại vật tư */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Loại vật tư:</Label>
          <div className='w-64'>
            <FormField
              name='ma_lvt'
              type='select'
              options={[
                { value: 'dich_vu', label: 'Dịch vụ' },
                { value: 'vat_tu', label: 'Vật tư' },
                { value: 'phu_tung', label: 'Phụ tùng' },
                { value: 'ccld', label: 'CCLĐ' },
                { value: 'ban_thanh_pham', label: 'Bán thành phẩm' },
                { value: 'thanh_pham', label: 'Thành phẩm' },
                { value: 'hang_hoa', label: 'Hàng hóa' },
                { value: 'hang_gia_cong', label: 'Hàng gia công' }
              ]}
              className='w-full'
            />
          </div>
          {/* Chỉ xem vật tư có theo dõi tồn kho */}
          <div className='flex items-center'>
            <FormField name='ton_kho_yn' type='checkbox' className='flex-1' />
            <Label className='w-80 min-w-40 text-left'>Chỉ xem vật tư có theo dõi tồn kho</Label>
          </div>
        </div>

        {/* Nhóm vật tư - 3 field trong 1 hàng */}
        <div className='flex items-center' aria-label='Nhóm vật tư'>
          <Label className='w-40 min-w-40 text-left'>Nhóm vật tư:</Label>
          <div className='flex w-[57.5%] gap-2'>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomVatTu1?.ma_nhom || ''}
                relatedFieldValue={state.nhomVatTu1?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomVatTu1}
                className='w-full'
              />
            </div>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư 2'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomVatTu2?.ma_nhom || ''}
                relatedFieldValue={state.nhomVatTu2?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomVatTu2}
                className='w-full'
              />
            </div>
            <div className='flex-1'>
              <SearchField<Group>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.NHOM_VAT_TU}/`}
                searchColumns={nhomColumns}
                dialogTitle='Danh mục nhóm vật tư 3'
                columnDisplay={`ma_nhom`}
                displayRelatedField={`ten_phan_nhom`}
                value={state.nhomVatTu3?.ma_nhom || ''}
                relatedFieldValue={state.nhomVatTu3?.ten_phan_nhom || ''}
                onRowSelection={actions.setNhomVatTu3}
                className='w-full'
              />
            </div>
          </div>
        </div>

        {/* Mã kho */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mã kho:</Label>
          <div className='w-[57.5%]'>
            <SearchField<KhoHang>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.KHO_HANG}/`}
              searchColumns={warehouseSearchColumns}
              dialogTitle='Danh mục kho hàng'
              columnDisplay={`ma_kho`}
              displayRelatedField={`ten_kho`}
              value={state.khoHang?.ma_kho || ''}
              relatedFieldValue={state.khoHang?.ten_kho || ''}
              onRowSelection={actions.setKhoHang}
              className='w-full'
              classNameRelatedField='w-auto min-w-[300px] max-w-full'
            />
          </div>
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-40 min-w-40 text-left'>Mẫu báo cáo:</Label>
          <div className='w-64'>
            <FormField
              name='mau_bc'
              type='select'
              options={[
                { value: 10, label: 'Mẫu số lượng' },
                { value: 20, label: 'Mẫu số lượng và giá trị' },
                { value: 30, label: 'Mẫu số lượng và giá trị ngoại tệ' }
              ]}
              className='w-full'
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralTab;
