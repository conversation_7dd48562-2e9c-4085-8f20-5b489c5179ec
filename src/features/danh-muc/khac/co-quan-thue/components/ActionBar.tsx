import { Pencil, Plus, Trash, Copy, FileSearch, RefreshCw } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onGetDataClick?: () => void;
  onViewClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onGetDataClick,
  onViewClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick
}) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục cơ quan thuế</h1>}>
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}
    {onGetDataClick && (
      <AritoActionButton title='Lấy dữ liệu mới' icon={RefreshCw} onClick={onGetDataClick} disabled={isViewDisabled} />
    )}
    {onViewClick && <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={isViewDisabled} />}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick,
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportClick,
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
