import {
  <PERSON><PERSON>,
  FileSearch,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onSearchClick?: () => void;
  onGetDataClick?: () => void;
  onViewClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onExportClick?: () => void;
  onShowClick?: () => void;
  onShowSidebar?: () => void;
}

export const ActionBar = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onSearchClick,
  onCopyClick,
  onGetDataClick,
  onViewClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onShowClick,
  onShowSidebar
}: ActionBarProps) => (
  <AritoActionBar
    titleComponent={<h1 className='text-xl font-bold'>Nhóm phí</h1>}
    className='flex w-full items-center justify-between bg-white p-4'
  >
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}
    {onViewClick && <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} disabled={isViewDisabled} />}
    <AritoMenuButton
      items={[
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: onSearchClick,
          group: 0
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: onRefreshClick,
          group: 1
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick,
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={18} />,
          onClick: onExportClick,
          group: 2
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: () => {},
          group: 3
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: () => {},
          group: 3
        }
      ]}
    />
  </AritoActionBar>
);
