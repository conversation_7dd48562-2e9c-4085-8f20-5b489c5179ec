import {
  Binoculars,
  FileDown,
  FileSpreadsheet,
  FileText,
  FileUp,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Table,
  Trash
} from 'lucide-react';
import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export function AssetChangeReasonActionBar({ className, onAddClick, onEditClick, isEditDisabled = true }: Props) {
  return (
    <AritoActionBar
      titleComponent={
        <div className='flex items-center gap-2'>
          <h1 className='text-xl font-bold'><PERSON>h mục lý do tăng giảm tài sản/công cụ</h1>
        </div>
      }
    >
      <div className={`grid gap-2 lg:flex ${className}`}>
        <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
        <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
        <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
        <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} />
        <AritoActionButton title='Tìm kiếm' icon={Binoculars} onClick={() => {}} />
        <AritoMenuButton
          title='Khác'
          items={[
            {
              title: 'Refresh',
              icon: RefreshCw,
              onClick: () => {},
              group: 0
            },
            {
              title: 'Cố định cột',
              icon: Table,
              onClick: () => {},
              group: 0
            },
            {
              title: 'In nhiều',
              icon: Printer,
              onClick: () => {},
              group: 1
            },
            {
              title: 'Kết xuất dữ liệu',
              icon: FileDown,
              onClick: () => {},
              group: 1
            },
            {
              title: 'Tải mẫu Excel',
              icon: FileSpreadsheet,
              onClick: () => {},
              group: 2
            },
            {
              title: 'Lấy dữ liệu từ Excel',
              icon: FileUp,
              onClick: () => {},
              group: 2
            },
            {
              title: 'Xóa',
              icon: Trash,
              onClick: () => {},
              group: 3
            }
          ]}
        />
      </div>
    </AritoActionBar>
  );
}
