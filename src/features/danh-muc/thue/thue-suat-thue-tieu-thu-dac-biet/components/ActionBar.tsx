import { Eye, FileDown, FileText, Pencil, Plus, RefreshCw, Table, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  isEditDisabled = true,
  onViewClick,
  isViewDisabled = true
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục thuế suất tiêu thụ đặc biệt</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={() => {}} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);
