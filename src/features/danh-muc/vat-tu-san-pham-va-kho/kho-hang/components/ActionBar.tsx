import { <PERSON><PERSON><PERSON>ch, <PERSON>Text, <PERSON>cil, Plus, Refresh<PERSON>w, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
// import { AritoMenuActionButton } from './arito-menu-action-custom';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
  onShowSidebar?: () => void;
  onViewClick?: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintClick,
  onShowSidebar,
  onViewClick
}: ActionBarProps) => (
  <AritoActionBar
    titleComponent={<h1 className='text-xl font-bold'>Danh mục kho hàng</h1>}
    className='flex w-full items-center justify-between bg-white p-4'
  >
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
    <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
    <AritoActionButton title='Xem' icon={FileSearch} onClick={onViewClick} />
    <AritoMenuButton
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={18} />,
          onClick: () => {},
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
