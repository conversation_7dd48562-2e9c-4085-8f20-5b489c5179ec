import { Plus, Edit, Lock, RefreshCcw, Trash2, X, Pin, LogOut } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';

/** Props for the GroupTabActions component */
interface GroupTabActionsProps {
  /** <PERSON><PERSON> for adding a new group */
  onAdd?: () => void;
  /** <PERSON><PERSON> for editing a group */
  onEdit?: () => void;
  /** <PERSON><PERSON> for managing group authorization */
  onAuthorize?: () => void;
  /** <PERSON><PERSON> for refreshing group data */
  onRefresh?: () => void;
  /** <PERSON><PERSON> for toggling column pin state */
  onTogglePin?: () => void;
  /** <PERSON><PERSON> for deleting a group */
  onDelete?: () => void;
  /** <PERSON><PERSON> for closing the tab */
  onClose?: () => void;
}

/**
 * Action buttons component for the Group tab in user management.
 * Provides buttons for common group management operations like add, edit, authorize, etc.
 */
export const GroupTabActions: React.FC<GroupTabActionsProps> = ({
  onAdd,
  onEdit,
  onAuthorize,
  onRefresh,
  onTogglePin,
  onDelete,
  onClose
}) => {
  return (
    <div className='flex items-center gap-2'>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} variant='secondary' />
      <AritoActionButton title='Sửa' icon={Edit} onClick={onEdit} variant='secondary' />
      <AritoActionButton title='Phân quyền' icon={Lock} onClick={onAuthorize} variant='secondary' />
      <AritoActionButton title='Làm tươi' icon={RefreshCcw} onClick={onRefresh} variant='secondary' />
      <AritoActionButton title='Ghim cột' icon={Pin} onClick={onTogglePin} variant='secondary' />
      <AritoActionButton title='Đóng' icon={LogOut} onClick={onClose} variant='destructive' />
      <AritoActionButton title='Xóa' icon={Trash2} onClick={onDelete} variant='destructive' />
    </div>
  );
};
