import { Pencil, Plus, FileText, RefreshCw, Table, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onCreate: () => void;
  onRefresh: () => void;
  onPinColumns: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export function ActionBar({ className, onCreate, onRefresh, onPinColumns, isEditDisabled = true }: Props) {
  return (
    <AritoActionBar
      titleComponent={<h1 className='relative text-xl font-bold'>Phân quyền truy cập theo đơn vị</h1>}
      className='border-b border-b-gray-200'
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onCreate} variant='primary' shortcut='Alt + N' />
      <AritoActionButton
        title='Sửa'
        icon={Pencil}
        shortcut='Alt + E, Ctrl + E'
        // onClick={() => {
        //   if (selectedItem) {
        //     handleUpdate(selectedItem);
        //   }
        // }}
        // disabled={!selectedItem}
      />
      <AritoActionButton
        title='Xóa'
        icon={Trash}
        shortcut='Alt + D, Ctrl + D'
        // onClick={handleDelete}
        disabled={isEditDisabled}
      />

      <AritoActionButton title='Sao chép' icon={FileText} shortcut='Alt + U' onClick={onPinColumns} />
      <AritoActionButton title='Cố định cột' icon={Table} onClick={() => {}} />
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} />
      <AritoMenuButton
        items={[
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
}
