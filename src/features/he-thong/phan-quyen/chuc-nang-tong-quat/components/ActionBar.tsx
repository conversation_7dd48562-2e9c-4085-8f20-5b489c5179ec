import { Pencil, Plus, FileText, RefreshCw, Table, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface Props {
  onCreate: () => void;
  onRefresh: () => void;
  className?: string;
  isEditDisabled?: boolean;
}

export function ActionBar({ className, onCreate, onRefresh, isEditDisabled = true }: Props) {
  return (
    <AritoActionBar titleComponent={<h1 className='relative text-xl font-bold'>Phân quyền các chức năng tổng quát</h1>}>
      <>
        <AritoActionButton title='Thêm' icon={Plus} onClick={onCreate} variant='primary' shortcut='Alt + N' />
        <AritoActionButton title='Sửa' icon={Pencil} shortcut='Alt + E, Ctrl + E' />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} />
      </>
    </AritoActionBar>
  );
}
