import { Pencil, Plus, RefreshCw, Table, FileDown, Printer, Trash, Copy } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAdd: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onCopy: () => void;
  onRefresh: () => void;
  isEditDisabled: boolean;
  className?: string;
}

export function ActionBar({ className, onAdd, onEdit, onDelete, onCopy, onRefresh }: Props) {
  return (
    <AritoActionBar
      titleComponent={<h1 className='relative text-xl font-bold'>Giới hạn truy cập trạng thái của chứng từ</h1>}
    >
      <>
        <AritoActionButton title='Thêm' icon={Plus} onClick={onAdd} variant='primary' />
        <AritoActionButton title='Sửa' icon={Pencil} onClick={onEdit} />
        <AritoActionButton title='Xóa' icon={Trash} onClick={onDelete} />
        <AritoActionButton title='Sao chép' icon={Copy} onClick={onCopy} />
        <AritoActionButton title='Cố định cột' icon={Table} onClick={() => {}} />
        <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefresh} />
        <AritoMenuButton
          items={[
            {
              title: 'Kết xuất dữ liệu',
              icon: <AritoIcon icon={555} />,
              onClick: () => {},
              group: 0
            }
          ]}
        />
      </>
    </AritoActionBar>
  );
}
