import { Pin, Search, FileDown, RefreshCw } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onPrintClick: () => void;
  onPrintEditClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onRefreshClick,
  onPrintEditClick,
  onPrintClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Nhật ký hệ thống</h1>}>
    <AritoActionButton
      title='Tìm kiếm'
      icon={Search}
      onClick={() => {
        onSearchClick();
      }}
    />
    <AritoActionButton
      title='Làm tươi'
      icon={RefreshCw}
      onClick={() => {
        onRefreshClick();
      }}
    />
    <AritoActionButton title='Ghim cột' icon={Pin} onClick={() => {}} />
    <AritoActionButton title='Kết xuất dữ liệu' icon={FileDown} onClick={() => {}} />
  </AritoActionBar>
);
