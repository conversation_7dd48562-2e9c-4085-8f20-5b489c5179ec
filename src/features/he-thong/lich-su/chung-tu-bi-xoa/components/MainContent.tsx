import AritoDataTables from '@/components/custom/arito/data-tables';
import { ChungTuBiXoaState } from '../hooks/use-chung-tu-bi-xoa';
import { ActionBar } from './ActionBar';

interface MainContentProps {
  state: ChungTuBiXoaState;
  tables: {
    name: string;
    rows: any[];
    columns: any[];
  }[];
  actions: {
    handleRowClick: any;
    handleAddClick: () => void;
    handleEditClick: () => void;
    handleSearchClick: () => void;
    handleRefreshClick: () => void;
    handlePrintClick: () => void;
    handlePrintEditClick: () => void;
  };
}

export const MainContent = ({ state, tables, actions }: MainContentProps) => {
  if (!state.initialFormSubmitted) {
    return null;
  }

  return (
    <>
      <ActionBar
        printedSample={state.printedSample}
        onAddClick={actions.handleAddClick}
        onEditClick={actions.handleEditClick}
        onDeleteClick={() => {}}
        onCopyClick={() => {}}
        onSearchClick={actions.handleSearchClick}
        onRefreshClick={actions.handleRefreshClick}
        onPrintClick={actions.handlePrintClick}
        onPrintEditClick={actions.handlePrintEditClick}
      />
      <div className='h-[calc(100vh-20%)] w-full overflow-hidden'>
        <AritoDataTables
          tables={tables}
          onRowClick={actions.handleRowClick}
          selectedRowId={state.selectedRowIndex || undefined}
        />
      </div>
    </>
  );
};
