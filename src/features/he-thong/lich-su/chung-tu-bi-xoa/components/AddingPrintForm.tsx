import React, { useState, useRef, useCallback, useEffect } from 'react'; // Added React hooks
import { useFormContext } from 'react-hook-form';
import { PopupFormField } from '@/components/arito/arito-form/pop-up/arito-popup-form-field';
import { getChangingValuePrinterSampleColumns } from '../print-cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

// Removed Split import

const MIN_PANEL_HEIGHT = 100; // Minimum height for the draggable panel
const INITIAL_PANEL_HEIGHT = 300; // Initial height

export const AddingPrintForm = () => {
  const { control } = useFormContext();
  const deleteSample = () => {};
  const copySample = () => {};
  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
  };

  const [isDragging, setIsDragging] = useState(false);
  const [panelHeight, setPanelHeight] = useState(INITIAL_PANEL_HEIGHT);
  const containerRef = useRef<HTMLDivElement>(null);
  const dragStartRef = useRef<{ startY: number; startHeight: number } | null>(null);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (!containerRef.current) return;
      setIsDragging(true);
      dragStartRef.current = {
        startY: e.clientY,
        startHeight: panelHeight
      };
      // Prevent text selection during drag
      e.preventDefault();
    },
    [panelHeight]
  );

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging || !dragStartRef.current || !containerRef.current) return;

      const currentY = e.clientY;
      const deltaY = dragStartRef.current.startY - currentY;
      const containerHeight = containerRef.current.offsetHeight;

      let newHeight = dragStartRef.current.startHeight + deltaY;

      // Clamp height within bounds
      newHeight = Math.max(MIN_PANEL_HEIGHT, newHeight); // Ensure minimum height
      newHeight = Math.min(containerHeight - 50, newHeight); // Ensure it doesn't cover everything (leave 50px for top)

      setPanelHeight(newHeight);
    },
    [isDragging]
  );

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      dragStartRef.current = null;
    }
  }, [isDragging]);

  // Add/Remove global listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    }

    // Cleanup function
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div ref={containerRef} className='relative flex flex-1 flex-col overflow-hidden'>
      <div className='flex-1 overflow-y-auto p-6' style={{ paddingBottom: `${panelHeight + 10}px` }}>
        <div className='grid w-full gap-5'>
          <div className='flex items-center gap-4'>
            <div>
              <PopupFormField
                control={control}
                type='select'
                label='Loại giấy'
                name='paperType'
                labelWidth='160px'
                inputWidth='380px'
                withSearch={true}
                searchEndpoint='FixedAsset'
                searchColumns={[
                  { field: 'paperType', headerName: 'Loại giấy' },
                  { field: 'description', headerName: 'Mô tả' }
                ]}
                defaultSearchColumn='paperType'
                withMultiSearch={false}
                options={[{ label: 'Thêm mới mẫu', value: '0' }]}
              />
            </div>
            <div className='flex gap-2'>
              <button className='rounded border px-4 py-1.5 text-sm hover:bg-slate-100' onClick={() => deleteSample()}>
                Xóa mẫu
              </button>
              <button className='rounded border px-4 py-1.5 text-sm hover:bg-slate-100' onClick={() => copySample()}>
                Sao chép mẫu
              </button>
            </div>
          </div>
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Nhập tên mẫu mới'
            name='newPrintSampleName'
            labelWidth='160px'
            inputWidth='380px'
          />
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tên khác'
            name='otherName'
            labelWidth='160px'
            inputWidth='380px'
          />
          <div className='grid items-center md:grid-cols-[160px,_1fr]'>
            <div className='pb-1 pl-2 text-sm'>Mẫu</div> {/* Adjusted text size for consistency */}
            <div className='flex items-center gap-1' style={{ width: '380px' }}>
              {' '}
              {/* Adjusted width to match others */}
              <PopupFormField
                control={control}
                className='items-center'
                type='select'
                label=''
                name='period'
                labelWidth='0px'
                inputWidth='240px'
                options={[
                  { label: 'Mẫu Kế toán trưởng ký', value: '0' },
                  { label: 'Mẫu Kế toán trưởng/giám đốc ký', value: '1' },
                  { label: 'Mẫu người lập ký', value: '2' }
                ]}
              />
              <PopupFormField
                control={control}
                className='items-center'
                type='select'
                label=''
                name='period_year'
                labelWidth='0px'
                inputWidth='130px'
                options={[
                  { label: 'Giấy in ngang', value: '0' },
                  { label: 'Giấy in đứng', value: '1' }
                ]}
              />
            </div>
          </div>
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tiêu đề in'
            name='printTitle'
            labelWidth='160px'
            inputWidth='380px'
          />
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tiêu đề 2'
            name='printTitle2'
            labelWidth='160px'
            inputWidth='380px'
          />
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tiêu đề phụ'
            name='subTitle'
            labelWidth='160px'
            inputWidth='380px'
          />
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tiêu đề phụ 2'
            name='subTitle2'
            labelWidth='160px'
            inputWidth='380px'
          />
          {/* Bilingual, Total lines, and Print status */}
          <div className='flex items-center gap-4'>
            <div className=''>
              <PopupFormField
                control={control}
                type='select'
                label='Mẫu song ngữ'
                name='bilingualMode'
                labelWidth='160px'
                inputWidth='120px'
                options={[
                  { label: 'Không', value: '0' },
                  { label: 'Có', value: '1' }
                ]}
              />
            </div>
            <div className='flex gap-4'>
              <PopupFormField
                control={control}
                type='select'
                label='In các dòng tổng'
                name='printTotalLines'
                labelWidth='120px'
                inputWidth='120px'
                options={[
                  { label: 'Không in', value: '0' },
                  { label: 'Có in', value: '1' }
                ]}
              />
            </div>
          </div>
          <PopupFormField
            control={control}
            className='items-center'
            type='select'
            label='Loại mẫu'
            name='templateType'
            labelWidth='160px'
            inputWidth='380px'
            options={[
              { label: 'Mẫu cho người dùng hiện tại', value: '0' },
              { label: 'Mẫu cho tất cả người dùng', value: '1' }
            ]}
          />
          <PopupFormField
            control={control}
            className='items-center'
            type='text'
            label='Tên file được tạo'
            name='generatedFileName'
            labelWidth='160px'
            inputWidth='380px'
          />
          {/* ... All other PopupFormField components remain here ... */}
        </div>
      </div>
      {/* Removed extra closing div here */}

      {/* Draggable Table Panel */}
      <div
        className='absolute bottom-0 left-0 right-0 flex flex-col border-t border-gray-300 bg-white shadow-lg'
        style={{ height: `${panelHeight}px` }}
      >
        {/* Drag Handle */}
        <div
          className='flex h-4 cursor-grab items-center justify-center bg-gray-200 hover:bg-gray-300 active:cursor-grabbing'
          onMouseDown={handleMouseDown}
        >
          {/* Optional: Add visual indicator like dots */}
          <div className='h-1 w-8 rounded-full bg-gray-400'></div>
        </div>

        {/* Table Container within Panel */}
        <div className='flex-1 overflow-hidden'>
          {' '}
          {/* Let table handle its own scroll if needed */}
          <AritoInputTable
            // value={[]} // Keep value commented/dynamic if needed
            columns={getChangingValuePrinterSampleColumns}
            // mode={formMode}
          />
        </div>
      </div>
    </div>
  );
};
