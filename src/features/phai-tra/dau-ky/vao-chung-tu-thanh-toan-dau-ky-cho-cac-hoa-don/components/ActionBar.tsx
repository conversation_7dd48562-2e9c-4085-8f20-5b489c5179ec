import { FileText, Pencil, Plus, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export const ActionBar = ({ className, onAddClick, onEditClick, isEditDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Chứng từ thanh toán đầu kỳ cho các hoá đơn</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={18} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: <AritoIcon icon={883} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Xóa',
          icon: <AritoIcon icon={8} />,
          onClick: () => {},
          group: 3
        }
      ]}
    />
  </AritoActionBar>
);
