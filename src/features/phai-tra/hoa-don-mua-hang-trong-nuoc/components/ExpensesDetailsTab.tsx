import { domesticInvoiceExpenseDetailsColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface ExpensesDetailsTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const ExpensesDetailsTab = ({ value, onChange, formMode }: ExpensesDetailsTabProps) => {
  return (
    <div className='h-[calc(100vh-600px)] w-screen'>
      <AritoInputTable
        value={value}
        columns={domesticInvoiceExpenseDetailsColumns}
        onChange={onChange}
        mode={formMode}
      />
    </div>
  );
};
