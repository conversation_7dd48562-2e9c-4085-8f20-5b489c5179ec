import { domesticInvoiceItemsColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface LineItemsTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const LineItemsTab = ({ value, onChange, formMode }: LineItemsTabProps) => {
  return (
    <div className='h-[calc(100vh-600px)] w-screen'>
      <AritoInputTable value={value} columns={domesticInvoiceItemsColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
