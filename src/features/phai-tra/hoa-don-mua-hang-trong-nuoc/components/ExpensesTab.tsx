import { domesticInvoiceExpenseColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface ExpensesTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const ExpensesTab = ({ value, onChange, formMode }: ExpensesTabProps) => {
  return (
    <div className='h-[calc(100vh-600px)] w-screen'>
      <AritoInputTable value={value} columns={domesticInvoiceExpenseColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
