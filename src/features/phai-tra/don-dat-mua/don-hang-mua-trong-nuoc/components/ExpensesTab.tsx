import { AritoInputTable } from '@/components/custom/arito';
import { orderExpenseColumns } from '../cols-definition';

interface ExpensesTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const ExpensesTab = ({ value, onChange, formMode }: ExpensesTabProps) => {
  return (
    <div className='h-[calc(100vh-500px)] w-screen'>
      <AritoInputTable value={value} columns={orderExpenseColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
