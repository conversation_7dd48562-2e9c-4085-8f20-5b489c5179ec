import {
  ArrowLeftRight,
  Binoculars,
  FileDown,
  FileText,
  Pencil,
  Plus,
  Printer,
  RefreshCw,
  Search,
  Table,
  Trash
} from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  className?: string;
}

export const ActionBar = ({ className, onAddClick, onEditClick, isEditDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu thanh toán tạm ứng</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='In ấn' icon={Printer} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: RefreshCw,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: Table,
          onClick: () => {},
          group: 0
        },
        {
          title: 'In nhiều',
          icon: Printer,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: FileDown,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Tìm kiếm',
          icon: Binoculars,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Xử lí chênh lệch',
          icon: ArrowLeftRight,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Xóa',
          icon: Trash,
          onClick: () => {},
          group: 3
        }
      ]}
    />
  </AritoActionBar>
);
