import { advancePaymentVoucherDetailColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';
interface DetailsTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const DetailsTab = ({ value, onChange, formMode }: DetailsTabProps) => {
  return (
    <div className='h-[calc(100vh-600px)] w-screen'>
      <AritoInputTable value={value} columns={advancePaymentVoucherDetailColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
