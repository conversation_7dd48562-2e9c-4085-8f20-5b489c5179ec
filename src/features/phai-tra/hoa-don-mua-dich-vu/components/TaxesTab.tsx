import { serviceInvoiceTaxColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface TaxesTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const TaxesTab = ({ value, onChange, formMode }: TaxesTabProps) => {
  return (
    <div className='h-[calc(100vh-500px)] w-screen'>
      <AritoInputTable value={value} columns={serviceInvoiceTaxColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
