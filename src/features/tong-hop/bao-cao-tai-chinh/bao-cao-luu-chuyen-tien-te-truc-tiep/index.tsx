'use client';

import { useState } from 'react';
import { useDialogState, useTableData, useActionHandlers, useBaoCaoLuuChuyenTienTeTrucTiep } from './hooks';
import { EditPrintTemplateDialog } from '@/components/custom/arito/edit-print-template';
import { AritoDataTables, LoadingOverlay } from '@/components/custom/arito';
import { SearchDialog, ActionBar } from './components';

export default function DirectCashFlowReportPage() {
  const {
    initialSearchDialogOpen,
    showTable,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick
  } = useDialogState();

  const [searchParams, setSearchParams] = useState<any>({});
  const { data, isLoading, fetchData, refreshData } = useBaoCaoLuuChuyenTienTeTrucTiep(searchParams);
  const { tables, handleRowClick } = useTableData(data, searchParams.mau_bc);
  const {
    showEditPrintTemplateDialog,
    openEditPrintTemplateDialog,
    closeEditPrintTemplateDialog,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick,
    handleEditPrintTemplateClick
  } = useActionHandlers();

  const handleSearchWithData = async (searchData: any) => {
    setSearchParams(searchData);
    await fetchData(searchData);
    handleInitialSearch(searchData);
  };

  const handleRefreshWithData = async () => {
    await refreshData();
    handleRefreshClick();
  };

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <SearchDialog open={initialSearchDialogOpen} onClose={handleInitialSearchClose} onSearch={handleSearchWithData} />

      <EditPrintTemplateDialog
        open={showEditPrintTemplateDialog}
        onClose={closeEditPrintTemplateDialog}
        onSave={handleEditPrintTemplateClick}
      />

      {showTable && (
        <>
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={handleRefreshWithData}
            onFixedColumnsClick={handleFixedColumnsClick}
            onExportDataClick={handleExportDataClick}
            onEditPrintTemplateClick={openEditPrintTemplateDialog}
            searchParams={searchParams}
          />

          <div className='flex-1 overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && <AritoDataTables tables={tables} onRowClick={handleRowClick} />}
          </div>
        </>
      )}
    </div>
  );
}
