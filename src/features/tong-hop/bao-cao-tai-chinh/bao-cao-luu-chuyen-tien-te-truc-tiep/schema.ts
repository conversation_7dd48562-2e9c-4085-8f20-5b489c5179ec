import { z } from 'zod';

export const searchSchema = z.object({
  // Basic fields for current period
  ngay_ct11: z.coerce.date(),
  ngay_ct12: z.coerce.date(),

  // Basic fields for previous period
  ngay_ct01: z.coerce.date(),
  ngay_ct02: z.coerce.date(),

  // Detail tab
  nguoi_lap: z.string().optional(),
  ma_unit: z.string().optional(),
  ten_unit: z.string().optional(),
  id_maubc: z.string().optional(),
  mau_bc: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct11: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct12: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  ngay_ct01: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
  ngay_ct02: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0),
  nguoi_lap: '',
  ma_unit: '',
  ten_unit: '',
  id_maubc: 'BCLTTTT01',
  mau_bc: '20'
};

// Schema for the edit print template form
export const editPrintTemplateSchema = z.object({
  paperType: z.string().optional(),
  templateName: z.string().min(1, 'Tên mẫu không được để trống'),
  otherName: z.string().optional(),
  template: z.string().optional(),
  paperOrientation: z.string().optional(),
  printTitle: z.string().optional(),
  printTitle2: z.string().optional(),
  subTitle: z.string().optional(),
  subTitle2: z.string().optional(),
  bilingualTemplate: z.string().optional(),
  printEmptyLines: z.string().optional(),
  templateType: z.string().optional(),
  fileName: z.string().optional(),
  column1: z.string().optional(),
  column2: z.string().optional(),
  column3: z.string().optional(),
  column4: z.string().optional(),
  column5: z.string().optional(),
  column6: z.string().optional(),
  column7: z.string().optional(),
  column8: z.string().optional()
});

export type EditPrintTemplateFormValues = z.infer<typeof editPrintTemplateSchema>;

export const initialPrintTemplateValues: EditPrintTemplateFormValues = {
  paperType: '',
  templateName: '',
  otherName: '',
  template: '',
  paperOrientation: 'landscape',
  printTitle: '',
  printTitle2: '',
  subTitle: '',
  subTitle2: '',
  bilingualTemplate: 'false',
  printEmptyLines: 'false',
  templateType: '',
  fileName: '',
  column1: '',
  column2: '',
  column3: '',
  column4: '',
  column5: '',
  column6: '',
  column7: '',
  column8: ''
};
