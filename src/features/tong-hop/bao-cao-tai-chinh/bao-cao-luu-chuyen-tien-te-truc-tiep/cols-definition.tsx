import { GridColDef } from '@mui/x-data-grid';
import React from 'react';

// Search columns for employee search
export const employeeSearchColumns: GridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên', width: 150 },
  { field: 'ten_nv', headerName: 'Tên nhân viên', width: 250 },
  { field: 'phong_ban', headerName: 'Phòng ban', width: 200 },
  { field: 'chuc_vu', headerName: 'Chức vụ', width: 150 }
];

// Search columns for unit search
export const unitSearchColumns: GridColDef[] = [
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 150 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 250 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 300 }
];

// 1. "Mẫu tiền chuẩn - năm" (value: '20')
export const standardYearColumns: GridColDef[] = [
  {
    field: 'chi_tieu',
    headerName: 'Chỉ tiêu',
    width: 350,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ma_so',
    headerName: 'Mã số',
    width: 100,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  {
    field: 'ky_nay',
    headerName: 'Kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc',
    headerName: 'Kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  }
];

// 2. "Mẫu tiền chuẩn - giữa biên độ" (value: '21')
export const standardInterimColumns: GridColDef[] = [
  {
    field: 'chi_tieu',
    headerName: 'Chỉ tiêu',
    width: 350,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  {
    field: 'ky_nay',
    headerName: 'Kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc',
    headerName: 'Kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_nay',
    headerName: 'Lũy kế kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_truoc',
    headerName: 'Lũy kế kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  }
];

// 3. "Mẫu tiền nt - năm" (value: '30')
export const foreignCurrencyYearColumns: GridColDef[] = [
  {
    field: 'chi_tieu',
    headerName: 'Chỉ tiêu',
    width: 350,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  {
    field: 'ky_nay_nt',
    headerName: 'Kỳ này NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc_nt',
    headerName: 'Kỳ trước NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_nay',
    headerName: 'Kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc',
    headerName: 'Kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  }
];

// 4. "Mẫu tiền nt - giữa biên độ" (value: '31')
export const foreignCurrencyInterimColumns: GridColDef[] = [
  {
    field: 'chi_tieu',
    headerName: 'Chỉ tiêu',
    width: 350,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  {
    field: 'ky_nay_nt',
    headerName: 'Kỳ này NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc_nt',
    headerName: 'Kỳ trước NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_nay_nt',
    headerName: 'Lũy kế kỳ này NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_truoc_nt',
    headerName: 'Lũy kế kỳ trước NT',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_nay',
    headerName: 'Kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'ky_truoc',
    headerName: 'Kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_nay',
    headerName: 'Lũy kế kỳ này',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  },
  {
    field: 'luy_ke_ky_truoc',
    headerName: 'Lũy kế kỳ trước',
    width: 150,
    renderCell: (params: any) => {
      const isBoldRow = params.row.xchi_tieu === 'T' || params.row.dau_cuoi === '1';
      return <span style={{ fontWeight: isBoldRow ? 'bold' : 'normal' }}>{params.value}</span>;
    }
  }
];

// Map template values to column definitions
export const reportTemplateColumnsMap: Record<string, GridColDef[]> = {
  '20': standardYearColumns,
  '21': standardInterimColumns,
  '30': foreignCurrencyYearColumns,
  '31': foreignCurrencyInterimColumns
};

// Default columns (for backward compatibility)
export const directCashFlowReportColumns = standardYearColumns;
