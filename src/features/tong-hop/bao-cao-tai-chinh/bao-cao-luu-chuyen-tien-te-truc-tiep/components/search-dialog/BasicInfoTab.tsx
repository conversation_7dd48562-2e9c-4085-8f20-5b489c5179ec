import React from 'react';
import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-6 p-4'>
      <div className='space-y-4'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'><PERSON><PERSON> này từ/đến:</Label>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct11' toDateName='ngay_ct12' />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'><PERSON><PERSON> trước từ/đến:</Label>
          <AritoFormDateRangeDropdown fromDateName='ngay_ct01' toDateName='ngay_ct02' />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
