import React from 'react';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-4 p-4'>
      {/* Người lập */}
      <div className='flex items-center gap-4'>
        <Label className='w-32 min-w-32 text-sm font-medium'>Người lập</Label>
        <div className='w-full'>
          <FormField labelClassName='min-w-36' name='nguoi_lap' disabled={formMode === 'view'} />
        </div>
      </div>

      {/* Chọn báo cáo */}
      <div className='flex items-center gap-4'>
        <Label className='w-32 min-w-32 text-sm font-medium'>Chọn báo cáo:</Label>
        <div className='w-[350px]'>
          <FormField
            name='id_maubc'
            type='select'
            label=''
            disabled={formMode === 'view'}
            options={[{ label: 'Báo cáo LCTT trực tiếp - Thông tư 200', value: 'BCLTTTT01' }]}
          />
        </div>
        <div className='ml-2 h-9 w-9 flex-shrink-0'>
          <RadixHoverDropdown
            iconNumber={'...'}
            items={[
              {
                value: 'create',
                label: 'Tạo mẫu báo cáo mới',
                icon: 7,
                onClick: () => console.log('Create new report template')
              },
              {
                value: 'edit',
                label: 'Sửa mẫu đang chọn',
                icon: 9,
                onClick: () => console.log('Edit current report template')
              },
              {
                value: 'delete',
                label: 'Xóa mẫu đang chọn',
                icon: 8,
                onClick: () => console.log('Delete current report template')
              }
            ]}
          />
        </div>
      </div>

      {/* Mẫu báo cáo */}
      <div className='flex items-center gap-4'>
        <Label className='w-32 min-w-32 text-sm font-medium'>Mẫu báo cáo:</Label>
        <div className='w-[350px]'>
          <FormField
            name='mau_bc'
            type='select'
            label=''
            disabled={formMode === 'view'}
            options={[
              { label: 'Mẫu tiền chuẩn - năm', value: '20' },
              { label: 'Mẫu tiền chuẩn - giữa biên độ', value: '21' },
              { label: 'Mẫu tiền nt - năm', value: '30' },
              { label: 'Mẫu tiền nt - giữa biên độ', value: '31' }
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default DetailTab;
