import { useMemo } from 'react';
import { standardYearColumns, reportTemplateColumnsMap } from '../cols-definition';

export function useTableData(data: any[], mau_bc?: string) {
  const tables = useMemo(() => {
    if (!data || data.length === 0) return [];

    // Get the appropriate columns based on mau_bc value
    const columns = mau_bc && reportTemplateColumnsMap[mau_bc] ? reportTemplateColumnsMap[mau_bc] : standardYearColumns;

    return [
      {
        name: '',
        columns: columns,
        rows: data
      }
    ];
  }, [data, mau_bc]);

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick
  };
}
