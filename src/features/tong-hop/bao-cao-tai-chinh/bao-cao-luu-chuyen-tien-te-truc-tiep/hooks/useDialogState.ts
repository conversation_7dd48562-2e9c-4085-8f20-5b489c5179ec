import { useState } from 'react';

interface UseDialogStateReturn {
  showTable: boolean;
  initialSearchDialogOpen: boolean;

  handleInitialSearchClose: () => void;
  handleInitialSearch: (values: any) => void;
  handleSearchClick: () => void;
}

export const useDialogState = (): UseDialogStateReturn => {
  const [showTable, setShowTable] = useState(false);
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: any) => {
    setShowTable(true);
    setInitialSearchDialogOpen(false);
    console.log('Search values:', values);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  return {
    showTable,
    initialSearchDialogOpen,

    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick
  };
};
