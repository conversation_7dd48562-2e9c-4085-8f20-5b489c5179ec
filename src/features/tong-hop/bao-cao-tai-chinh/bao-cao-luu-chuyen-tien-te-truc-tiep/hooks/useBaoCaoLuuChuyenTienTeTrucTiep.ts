import { useState, useCallback } from 'react';
import {
  BaoCaoLuuChuyenTienTeTrucTiepItem,
  BaoCaoLuuChuyenTienTeTrucTiepResponse,
  BaoCaoLuuChuyenTienTeTrucTiepSearchFormValues,
  UseBaoCaoLuuChuyenTienTeTrucTiepReturn
} from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): BaoCaoLuuChuyenTienTeTrucTiepItem[] => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  return [
    {
      id: '1',
      ma_so: '01',
      chi_tieu: 'I. LƯU CHUYỂN TIỀN TỪ HOẠT ĐỘNG KINH DOANH',
      thuyet_minh: '',
      tk: '',
      ky_nay: '',
      ky_truoc: '',
      ky_nay_nt: '',
      ky_truoc_nt: '',
      luy_ke_ky_nay: '',
      luy_ke_ky_truoc: '',
      luy_ke_ky_nay_nt: '',
      luy_ke_ky_truoc_nt: '',
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDKD',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '2',
      ma_so: '02',
      chi_tieu: '1. Tiền thu từ bán hàng, cung cấp dịch vụ và doanh thu khác',
      thuyet_minh: '01',
      tk: '511,512',
      ky_nay: (Math.random() * 2000000 + 800000).toFixed(0),
      ky_truoc: (Math.random() * 1500000 + 600000).toFixed(0),
      ky_nay_nt: (Math.random() * 100000 + 40000).toFixed(0),
      ky_truoc_nt: (Math.random() * 80000 + 30000).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 6000000 + 2400000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 4500000 + 1800000).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 300000 + 120000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 225000 + 90000).toFixed(0),
      xchi_tieu: '1',
      xchi_tieu2: 'TTBH',
      tk_du: 'C',
      dau_cuoi: '0'
    },
    {
      id: '3',
      ma_so: '03',
      chi_tieu: '2. Tiền chi trả cho người cung cấp hàng hóa và dịch vụ',
      thuyet_minh: '02',
      tk: '331',
      ky_nay: `(${(Math.random() * 800000 + 400000).toFixed(0)})`,
      ky_truoc: `(${(Math.random() * 600000 + 300000).toFixed(0)})`,
      ky_nay_nt: `(${(Math.random() * 40000 + 20000).toFixed(0)})`,
      ky_truoc_nt: `(${(Math.random() * 30000 + 15000).toFixed(0)})`,
      luy_ke_ky_nay: `(${(Math.random() * 2400000 + 1200000).toFixed(0)})`,
      luy_ke_ky_truoc: `(${(Math.random() * 1800000 + 900000).toFixed(0)})`,
      luy_ke_ky_nay_nt: `(${(Math.random() * 120000 + 60000).toFixed(0)})`,
      luy_ke_ky_truoc_nt: `(${(Math.random() * 90000 + 45000).toFixed(0)})`,
      xchi_tieu: '2',
      xchi_tieu2: 'CTNCC',
      tk_du: 'N',
      dau_cuoi: '0'
    },
    {
      id: '4',
      ma_so: '04',
      chi_tieu: '3. Tiền chi trả cho người lao động',
      thuyet_minh: '03',
      tk: '334',
      ky_nay: `(${(Math.random() * 300000 + 150000).toFixed(0)})`,
      ky_truoc: `(${(Math.random() * 250000 + 120000).toFixed(0)})`,
      ky_nay_nt: `(${(Math.random() * 15000 + 7500).toFixed(0)})`,
      ky_truoc_nt: `(${(Math.random() * 12500 + 6000).toFixed(0)})`,
      luy_ke_ky_nay: `(${(Math.random() * 900000 + 450000).toFixed(0)})`,
      luy_ke_ky_truoc: `(${(Math.random() * 750000 + 360000).toFixed(0)})`,
      luy_ke_ky_nay_nt: `(${(Math.random() * 45000 + 22500).toFixed(0)})`,
      luy_ke_ky_truoc_nt: `(${(Math.random() * 37500 + 18000).toFixed(0)})`,
      xchi_tieu: '3',
      xchi_tieu2: 'CTNLD',
      tk_du: 'N',
      dau_cuoi: '0'
    },
    {
      id: '5',
      ma_so: '20',
      chi_tieu: 'Lưu chuyển tiền thuần từ hoạt động kinh doanh',
      thuyet_minh: '',
      tk: '',
      ky_nay: (Math.random() * 400000 + 100000).toFixed(0),
      ky_truoc: (Math.random() * 300000 + 80000).toFixed(0),
      ky_nay_nt: (Math.random() * 20000 + 5000).toFixed(0),
      ky_truoc_nt: (Math.random() * 15000 + 4000).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 1200000 + 300000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 900000 + 240000).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 60000 + 15000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 45000 + 12000).toFixed(0),
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDKD',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '6',
      ma_so: '21',
      chi_tieu: 'II. LƯU CHUYỂN TIỀN TỪ HOẠT ĐỘNG ĐẦU TƯ',
      thuyet_minh: '',
      tk: '',
      ky_nay: '',
      ky_truoc: '',
      ky_nay_nt: '',
      ky_truoc_nt: '',
      luy_ke_ky_nay: '',
      luy_ke_ky_truoc: '',
      luy_ke_ky_nay_nt: '',
      luy_ke_ky_truoc_nt: '',
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDDT',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '7',
      ma_so: '22',
      chi_tieu: '1. Tiền chi để mua sắm, xây dựng tài sản cố định và các tài sản dài hạn khác',
      thuyet_minh: '04',
      tk: '211,213',
      ky_nay: `(${(Math.random() * 200000 + 100000).toFixed(0)})`,
      ky_truoc: `(${(Math.random() * 150000 + 80000).toFixed(0)})`,
      ky_nay_nt: `(${(Math.random() * 10000 + 5000).toFixed(0)})`,
      ky_truoc_nt: `(${(Math.random() * 7500 + 4000).toFixed(0)})`,
      luy_ke_ky_nay: `(${(Math.random() * 600000 + 300000).toFixed(0)})`,
      luy_ke_ky_truoc: `(${(Math.random() * 450000 + 240000).toFixed(0)})`,
      luy_ke_ky_nay_nt: `(${(Math.random() * 30000 + 15000).toFixed(0)})`,
      luy_ke_ky_truoc_nt: `(${(Math.random() * 22500 + 12000).toFixed(0)})`,
      xchi_tieu: '4',
      xchi_tieu2: 'CTSCD',
      tk_du: 'N',
      dau_cuoi: '0'
    },
    {
      id: '8',
      ma_so: '30',
      chi_tieu: 'Lưu chuyển tiền thuần từ hoạt động đầu tư',
      thuyet_minh: '',
      tk: '',
      ky_nay: `(${(Math.random() * 200000 + 100000).toFixed(0)})`,
      ky_truoc: `(${(Math.random() * 150000 + 80000).toFixed(0)})`,
      ky_nay_nt: `(${(Math.random() * 10000 + 5000).toFixed(0)})`,
      ky_truoc_nt: `(${(Math.random() * 7500 + 4000).toFixed(0)})`,
      luy_ke_ky_nay: `(${(Math.random() * 600000 + 300000).toFixed(0)})`,
      luy_ke_ky_truoc: `(${(Math.random() * 450000 + 240000).toFixed(0)})`,
      luy_ke_ky_nay_nt: `(${(Math.random() * 30000 + 15000).toFixed(0)})`,
      luy_ke_ky_truoc_nt: `(${(Math.random() * 22500 + 12000).toFixed(0)})`,
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDDT',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '9',
      ma_so: '31',
      chi_tieu: 'III. LƯU CHUYỂN TIỀN TỪ HOẠT ĐỘNG TÀI CHÍNH',
      thuyet_minh: '',
      tk: '',
      ky_nay: '',
      ky_truoc: '',
      ky_nay_nt: '',
      ky_truoc_nt: '',
      luy_ke_ky_nay: '',
      luy_ke_ky_truoc: '',
      luy_ke_ky_nay_nt: '',
      luy_ke_ky_truoc_nt: '',
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDTC',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '10',
      ma_so: '32',
      chi_tieu: '1. Tiền thu từ phát hành cổ phiếu, nhận vốn góp của chủ sở hữu',
      thuyet_minh: '05',
      tk: '411',
      ky_nay: (Math.random() * 1000000 + 300000).toFixed(0),
      ky_truoc: (Math.random() * 500000 + 0).toFixed(0),
      ky_nay_nt: (Math.random() * 50000 + 15000).toFixed(0),
      ky_truoc_nt: (Math.random() * 25000 + 0).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 1000000 + 300000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 500000 + 0).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 50000 + 15000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 25000 + 0).toFixed(0),
      xchi_tieu: '5',
      xchi_tieu2: 'TTVON',
      tk_du: 'C',
      dau_cuoi: '0'
    },
    {
      id: '11',
      ma_so: '40',
      chi_tieu: 'Lưu chuyển tiền thuần từ hoạt động tài chính',
      thuyet_minh: '',
      tk: '',
      ky_nay: (Math.random() * 1000000 + 300000).toFixed(0),
      ky_truoc: (Math.random() * 500000 + 0).toFixed(0),
      ky_nay_nt: (Math.random() * 50000 + 15000).toFixed(0),
      ky_truoc_nt: (Math.random() * 25000 + 0).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 1000000 + 300000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 500000 + 0).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 50000 + 15000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 25000 + 0).toFixed(0),
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTHDTC',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '12',
      ma_so: '50',
      chi_tieu: 'Lưu chuyển tiền thuần trong kỳ',
      thuyet_minh: '',
      tk: '',
      ky_nay: (Math.random() * 800000 + 400000).toFixed(0),
      ky_truoc: (Math.random() * 300000 + 100000).toFixed(0),
      ky_nay_nt: (Math.random() * 40000 + 20000).toFixed(0),
      ky_truoc_nt: (Math.random() * 15000 + 5000).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 1000000 + 500000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 400000 + 200000).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 50000 + 25000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 20000 + 10000).toFixed(0),
      xchi_tieu: 'T',
      xchi_tieu2: 'LCTTK',
      tk_du: '',
      dau_cuoi: '1'
    },
    {
      id: '13',
      ma_so: '60',
      chi_tieu: 'Tiền và tương đương tiền đầu kỳ',
      thuyet_minh: '06',
      tk: '111,112',
      ky_nay: (Math.random() * 200000 + 80000).toFixed(0),
      ky_truoc: (Math.random() * 150000 + 60000).toFixed(0),
      ky_nay_nt: (Math.random() * 10000 + 4000).toFixed(0),
      ky_truoc_nt: (Math.random() * 7500 + 3000).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 200000 + 80000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 150000 + 60000).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 10000 + 4000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 7500 + 3000).toFixed(0),
      xchi_tieu: '6',
      xchi_tieu2: 'TTDDK',
      tk_du: 'N',
      dau_cuoi: '0'
    },
    {
      id: '14',
      ma_so: '70',
      chi_tieu: 'Tiền và tương đương tiền cuối kỳ',
      thuyet_minh: '07',
      tk: '111,112',
      ky_nay: (Math.random() * 1000000 + 500000).toFixed(0),
      ky_truoc: (Math.random() * 400000 + 200000).toFixed(0),
      ky_nay_nt: (Math.random() * 50000 + 25000).toFixed(0),
      ky_truoc_nt: (Math.random() * 20000 + 10000).toFixed(0),
      luy_ke_ky_nay: (Math.random() * 1200000 + 600000).toFixed(0),
      luy_ke_ky_truoc: (Math.random() * 500000 + 250000).toFixed(0),
      luy_ke_ky_nay_nt: (Math.random() * 60000 + 30000).toFixed(0),
      luy_ke_ky_truoc_nt: (Math.random() * 25000 + 12500).toFixed(0),
      xchi_tieu: '7',
      xchi_tieu2: 'TTCK',
      tk_du: 'N',
      dau_cuoi: '0'
    }
  ];
};

/**
 * Custom hook for managing BaoCaoLuuChuyenTienTeTrucTiep (Direct Cash Flow Report) data
 *
 * This hook provides functionality to fetch direct cash flow report data
 * with mock support for testing and development purposes.
 */
export function useBaoCaoLuuChuyenTienTeTrucTiep(
  searchParams: BaoCaoLuuChuyenTienTeTrucTiepSearchFormValues
): UseBaoCaoLuuChuyenTienTeTrucTiepReturn {
  const [data, setData] = useState<BaoCaoLuuChuyenTienTeTrucTiepItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: BaoCaoLuuChuyenTienTeTrucTiepSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      const response = await api.get<BaoCaoLuuChuyenTienTeTrucTiepResponse>(
        '/tong-hop/bao-cao-tai-chinh/bao-cao-luu-chuyen-tien-te-truc-tiep/',
        {
          mock: true,
          mockData,
          params: searchParams
        }
      );

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
