import { FileTex<PERSON>, Lock, Printer, RefreshCw, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({ onViewClick, isViewDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Sổ chi tiết của một tài khoản</h1>}>
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoActionButton title='In ấn' icon={Printer} onClick={onViewClick} disabled={isViewDisabled} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} disabled={isViewDisabled} />
    <AritoActionButton title='Cố định cột' icon={Lock} onClick={() => {}} disabled={isViewDisabled} />
    <AritoActionButton title='Kết xuất dữ liệu' icon={FileText} onClick={() => {}} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Chỉnh sửa mẫu in',
          icon: <AritoIcon icon={864} />,
          onClick: () => {},
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
