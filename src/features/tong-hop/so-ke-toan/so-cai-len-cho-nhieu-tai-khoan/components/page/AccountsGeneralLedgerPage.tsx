'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useEffect, useState } from 'react';
import { AritoPopupForm } from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { generalJournalFilterSchema, generalJournalSchema } from '../../schemas';
import { getAccountsGeneralLedgerColumns } from '../../cols-definition';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import AritoNotifyModal from '@/components/arito/arito-notify-modal';
import { AritoDataTables } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { DetailTab } from '../filter-tabs/DetailTab';
import { MainTab } from '../filter-tabs/MainTab';
import { ActionBar } from '../ActionBar';
export default function AccountsGeneralLedgerPage({ initialRows }: { initialRows: any[] }) {
  // Process initial data
  const processedRows = initialRows.map(row => ({
    ...row,
    id: row.name // Use the name field as the id
  }));

  // Initial form state
  const [initialFormSubmitted, setInitialFormSubmitted] = useState(false);
  const [showInitialForm, setShowInitialForm] = useState(true);
  const [initialFormData, setInitialFormData] = useState({
    from_date: '',
    to_date: '',
    account_type: '',
    journal_type: ''
  });

  // Form state
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(processedRows.length > 0 ? processedRows[0] : null);
  // Track the selected row index for the DataGrid
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(processedRows.length > 0 ? '0' : null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  const [notifyModal, setNotifyModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });
  //Initial
  const [rows, setRows] = useState<any[]>(processedRows);

  // Debug selected row and row IDs
  useEffect(() => {
    console.log('Selected object:', selectedObj);
    console.log('Selected row index:', selectedRowIndex);
  }, [selectedObj, selectedRowIndex]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleCloseInitialForm = () => {
    // Allow closing the initial form without submission
    setShowInitialForm(false);
    // Don't set initialFormSubmitted to true, so nothing displays after closing
  };

  const handleInitialFormSubmit = (data: any) => {
    console.log('Initial form data:', data);
    // Process the form data here - you might want to fetch data based on these filters
    // For now, we're just accepting whatever they submitted
    setInitialFormData(data);
    setInitialFormSubmitted(true);
    setShowInitialForm(false);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const row = params.row as any;
    setSelectedObj(row);
    // The id in params.row is the index as a string
    setSelectedRowIndex(params.row.id.toString());
  };

  const showNotifyModal = (message: string, onOk?: () => void) => {
    setNotifyModal({
      open: true,
      message: message,
      title: 'Thông báo',
      onConfirm: () => {
        setNotifyModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getAccountsGeneralLedgerColumns(handleOpenViewForm, () => {})
    }
  ];

  return (
    <div className='flex h-[calc(100vh-10%)] w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoNotifyModal
        open={notifyModal.open}
        onClose={() => setNotifyModal(prev => ({ ...prev, open: false }))}
        onConfirm={notifyModal.onConfirm}
        title={notifyModal.title}
        message={notifyModal.message}
      />

      {/* Initial Filter Form - Must be completed before seeing data */}
      <AritoPopupForm<any>
        key='initial-filter-form'
        open={showInitialForm}
        onClose={handleCloseInitialForm}
        mode='add'
        title='Sổ cái lên cho nhiều tài khoản'
        titleIcon={<AritoIcon icon={12} />}
        // initialData={initialFormData}
        onSubmit={handleInitialFormSubmit}
        schema={generalJournalFilterSchema}
        headerFields={
          <div className='flex flex-row gap-2'>
            <MainTab />
          </div>
        }
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab />
          }
        ]}
        maxWidth='md'
        fullWidth={true}
      />

      {/* View form */}
      <AritoPopupForm<any>
        key={`account-form-view-${showForm}`}
        open={showForm}
        onClose={handleCloseForm}
        mode='view'
        title='Xem bút toán kết chuyển'
        titleIcon={<AritoIcon icon={12} />}
        initialData={currentObj || undefined}
        schema={generalJournalSchema}
        tabs={
          [
            // {
            //   id: "more",
            //   label: "Khác",
            //   component: <MoreInfomationTab formMode="view" />,
            // },
          ]
        }
        maxWidth='md'
        fullWidth={true}
      />

      {/* Only show data tables and action bar after initial form is submitted */}
      {initialFormSubmitted && (
        <>
          <ActionBar onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)} isViewDisabled={!selectedObj} />
          <div className='w-full overflow-hidden'>
            <AritoDataTables
              tables={tables}
              onRowClick={handleRowClick}
              selectedRowId={selectedRowIndex || undefined}
            />
          </div>
        </>
      )}
    </div>
  );
}
