import { Check, Lock, RefreshCw } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onResolveClick?: () => void;
  onRefreshClick?: () => void;
  onPinClick?: () => void;
}

export const ActionBar = ({ onResolveClick, onRefreshClick, onPinClick }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Xử lý giao dịch chờ duyệt</h1>}>
    <AritoActionButton title='Xử lý giao dịch chờ duyệt' icon={Check} onClick={onResolveClick} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} />
    <AritoActionButton title='Cố định cột' icon={Lock} onClick={onPinClick} />
  </AritoActionBar>
);
