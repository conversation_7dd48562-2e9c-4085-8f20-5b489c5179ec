import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@mui/material';

export const getPendingTransactionProcessingColumns = (): GridColDef[] => [
  {
    field: 'select',
    headerName: '',
    width: 50,
    renderCell: (params: GridRenderCellParams) => <Checkbox />
  },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 120 },
  { field: 'ma_ct', headerName: 'Mã c/từ', width: 120 },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 180 },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120, type: 'date' },
  { field: 'so_ct', headerName: 'Số c/từ', width: 120 },
  { field: 'ma_kh', headerName: 'Mã khách hàng', width: 140 },
  { field: 'ten_kh', headerName: '<PERSON>ê<PERSON> khách hàng', width: 180 },
  { field: 'dien_giai', headerName: '<PERSON>ễn giải', width: 200 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 120 },
  { field: 'ty_gia', headerName: 'Tỷ giá', width: 100, type: 'number' },
  { field: 'ps_no_nt', headerName: 'Ps nợ nt', width: 120, type: 'number' },
  { field: 'ps_co_nt', headerName: 'Ps có nt', width: 120, type: 'number' },
  { field: 'ps_no', headerName: 'Ps nợ', width: 120, type: 'number' },
  { field: 'ps_co', headerName: 'Ps có', width: 120, type: 'number' }
];
