'use client';

import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { getPendingTransactionProcessingColumns } from './cols-definition';
import { useXuLyGiaoDichChoDuyet } from './hooks';
import { ActionBar } from './components';
import { useRows } from '@/hooks';

export default function XuLyGiaoDichChoDuyetPage() {
  const { selectedObj, selectedRowIndex, handleRowClick } = useRows();
  const { transactions, isLoading, refreshTransactions } = useXuLyGiaoDichChoDuyet();

  const tables = [
    {
      name: '',
      rows: transactions,
      columns: getPendingTransactionProcessingColumns()
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <ActionBar onResolveClick={() => {}} onRefreshClick={refreshTransactions} />
      <div className='w-full overflow-hidden'>
        {isLoading && <LoadingOverlay />}
        {!isLoading && (
          <AritoDataTables
            tables={tables}
            defaultTabIndex={0}
            onRowClick={handleRowClick}
            selectedRowId={selectedRowIndex || undefined}
          />
        )}
      </div>
    </div>
  );
}
