import { Align<PERSON><PERSON><PERSON>, Eye, Save, Search, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({ onViewClick, isViewDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bút toán kết chuyển</h1>}>
    <AritoActionButton title='Kết chuyển' icon={Save} onClick={() => {}} />
    <AritoActionButton title='Xoá kết chuyển' icon={Trash} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoActionButton title='Xem khai báo' icon={Eye} onClick={onViewClick} />
    <AritoActionButton title='Xem kết quả' icon={AlignLeft} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
