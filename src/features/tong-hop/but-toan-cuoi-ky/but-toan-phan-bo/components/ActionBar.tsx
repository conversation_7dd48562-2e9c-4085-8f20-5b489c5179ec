import { Check, Eye, Save, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({ onViewClick, isViewDisabled = true }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Bút toán phân bổ</h1>}>
    <AritoActionButton title='Tính hệ số' icon={Check} onClick={() => {}} />
    <AritoActionButton title='Phân bổ' icon={Save} onClick={() => {}} />
    <AritoActionButton title='Xoá phân bổ' icon={Trash} onClick={() => {}} />
    <AritoActionButton title='Xem' icon={Eye} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Xem định khoản',
          icon: <AritoIcon icon={658} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Tìm kiếm',
          icon: <AritoIcon icon={12} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 2
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);
