import { <PERSON>, FileText, Pencil, Plus, Trash } from 'lucide-react';
import { useState } from 'react';
import * as yup from 'yup';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface Props {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onViewClick?: () => void;
  isEditDisabled?: boolean;
  isViewDisabled?: boolean;
  className?: string;
}

export function SupplierReturnReceiptActionBar({
  className,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onViewClick,
  isEditDisabled = true,
  isViewDisabled = true
}: Props) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleOpenPopup = () => {
    setIsPopupOpen(true);
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  async function importFile(
    data: { [key: string]: any }[],
    id_column: string,
    doctype: string,
    option: string,
    maxError: number,
    schema: yup.ObjectSchema<any>,
    validation: (
      data: { [key: string]: any }[],
      schema: yup.ObjectSchema<any>
    ) => { type: string; message: string; position: string }[]
  ) {
    let errs = validation(data, schema);
    if (errs.length) {
      console.log(errs);
      return;
    }

    // Placeholder implementation - ERPNext functionality removed
    console.log('Import functionality disabled');
    console.log(errs);
  }

  return (
    <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Phiếu xuất trả lại nhà cung cấp</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
      <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick} disabled={isEditDisabled} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
      {onViewClick && <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />}
      <AritoMenuButton
        title='Khác'
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={18} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            group: 1
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: handleOpenPopup,
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
}
