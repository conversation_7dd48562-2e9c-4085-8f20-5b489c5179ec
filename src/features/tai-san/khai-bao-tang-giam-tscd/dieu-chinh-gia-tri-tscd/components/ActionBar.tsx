import { Copy, Edit, Plus, Search, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onSearchClick: () => void;
}

export const ActionBar = ({ onAddClick, onEditClick, onDeleteClick, onCopyClick, onSearchClick }: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Điều chỉnh giá trị tài sản cố định</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />
    <AritoActionButton title='Sửa' icon={Edit} onClick={() => {}} />
    <AritoActionButton title='Xoá' icon={Trash} onClick={() => {}} />
    <AritoActionButton title='Sao chép' icon={Copy} onClick={() => {}} />
    <AritoActionButton title='Tìm kiếm' icon={Search} onClick={() => {}} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Refresh',
          icon: <AritoIcon icon={15} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: () => {},
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: () => {},
          group: 1
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: () => {},
          group: 1
        }
      ]}
    />
  </AritoActionBar>
);
