import { Lock, Pencil, Plus, RefreshCw, Trash, Copy, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

/**
 * Props for the ActionBar component
 */
interface ActionBarProps {
  isViewDisabled?: boolean;
  onAddClick?: () => void;
  onEditClick?: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onSearchClick?: () => void;
  onFixedColumnsClick?: () => void;
  onRefreshClick?: () => void;
  onExportClick?: () => void;
  onDownloadTemplateClick?: () => void;
  onImportFromExcelClick?: () => void;
}

/**
 * ActionBar component for the Balance Sheet by Unit page
 */
const ActionBar: React.FC<ActionBarProps> = ({
  isViewDisabled = false,
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onSearchClick,
  onFixedColumnsClick,
  onRefreshClick,
  onExportClick,
  onDownloadTemplateClick,
  onImportFromExcelClick
}) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'>Danh mục tài sản cố định</h1>}>
    {onAddClick && <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} />}
    {onEditClick && <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isViewDisabled} />}
    {onDeleteClick && <AritoActionButton title='Xóa' icon={Trash} onClick={onDeleteClick} disabled={isViewDisabled} />}
    {onCopyClick && <AritoActionButton title='Copy' icon={Copy} onClick={onCopyClick} disabled={isViewDisabled} />}
    {onSearchClick && (
      <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} disabled={isViewDisabled} />
    )}
    {onRefreshClick && (
      <AritoActionButton title='Refresh' icon={RefreshCw} onClick={onRefreshClick} disabled={isViewDisabled} />
    )}

    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Cố định cột',
          icon: <AritoIcon icon={16} />,
          onClick: onFixedColumnsClick,
          group: 0
        },
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: onExportClick,
          group: 1
        },
        {
          title: 'Tải mẫu Excel',
          icon: <AritoIcon icon={28} />,
          onClick: onDownloadTemplateClick,
          group: 2
        },
        {
          title: 'Lấy dữ liệu từ Excel',
          icon: <AritoIcon icon={29} />,
          onClick: onImportFromExcelClick,
          group: 2
        }
      ]}
    />
  </AritoActionBar>
);

export default ActionBar;
