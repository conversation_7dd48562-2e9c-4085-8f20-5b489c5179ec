'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { assetSummaryReportColumns, getFixedRowsData } from '../cols-definition';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { assetSummaryReportSchema } from '../schemas';
import { DetailTab } from '../components/DetailTab';
import { ActionBar } from '../components/ActionBar';
import { FilterTab } from '../components/FilterTab';
import { OtherTab } from '../components/OtherTab';
import { formatYyyyMmDd } from '@/lib/utils';

export default function AssetSummaryReportPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<0 | 1 | 2>(0);
  const [rows, setRows] = useState<any[]>(getFixedRowsData());

  // Data loading state
  const [loading, setLoading] = useState<boolean>(false);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  const loadReportData = (data: any) => {
    const updatedRows = getFixedRowsData().map(row => {
      return {
        ...row,
        nha_cua_vat_kien_truc: data[row.id]?.nha_cua_vat_kien_truc || 0,
        may_moc_thiet_bi: data[row.id]?.may_moc_thiet_bi || 0
      };
    });

    setRows(updatedRows);
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const asset = params.row as any;
    setSelectedObj(asset);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = async (data: any) => {
    if (data.tu_ky && data.tu_nam && data.den_ky && data.den_nam) {
      console.log(`Period: ${data.tu_ky}/${data.tu_nam} - ${data.den_ky}/${data.den_nam}`);
    } else {
      console.log('No complete period provided');
    }

    setSelectedTable(data.mau_bao_cao);
    setLoading(true);

    // Here you would fetch the asset increase/decrease summary data based on form criteria
    setTimeout(() => {
      // Simulate data fetching
      setRows([]);
      setLoading(false);
    }, 1000);

    setShowForm(false);
  };

  const getReportTitle = () => {
    switch (selectedTable) {
      case 1:
        return 'Báo cáo tổng hợp tình hình tăng giảm TSCĐ theo bộ phận';
      case 2:
        return 'Báo cáo tổng hợp tình hình tăng giảm TSCĐ theo loại TSCĐ';
      default:
        return 'Báo cáo tổng hợp tình hình tăng giảm TSCĐ';
    }
  };

  let tables = [
    {
      name: getReportTitle(),
      rows: rows,
      columns: assetSummaryReportColumns(handleOpenViewForm, handleOpenEditForm)
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoPopupForm<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Báo cáo tổng hợp tình hình tăng giảm TSCĐ'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={assetSummaryReportSchema}
        headerFields={<FilterTab formMode={formMode} />}
        maxWidth='md'
        fullWidth={true}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        onDeleteClick={() => {}}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
        data={rows}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
