'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import { assetDetailIncreaseDecreaseReportColumns } from '../cols-definition';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { assetDetailIncreaseDecreaseSchema } from '../schemas';
import { DetailTab } from '../components/DetailTab';
import { ActionBar } from '../components/ActionBar';
import { FilterTab } from '../components/FilterTab';
import { OtherTab } from '../components/OtherTab';
import { formatYyyyMmDd } from '@/lib/utils';

export default function AssetDetailIncreaseDecreaseReportPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<0 | 1 | 2>(0);

  // Data loading state
  const [loading, setLoading] = useState<boolean>(false);

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  // Initial data
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name || row.ma_tai_san // Use name or asset code as the id
    }))
  );

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const asset = params.row as any;
    setSelectedObj(asset);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = async (data: any) => {
    if (data.tu_ky && data.tu_nam && data.den_ky && data.den_nam) {
      console.log(`Period: ${data.tu_ky}/${data.tu_nam} - ${data.den_ky}/${data.den_nam}`);
    } else {
      console.log('No complete period provided');
    }

    setSelectedTable(data.mau_bao_cao);
    setLoading(true);

    // Here you would fetch the asset increase/decrease data based on form criteria
    setTimeout(() => {
      // Simulate data fetching
      setRows([]);
      setLoading(false);
    }, 1000);

    setShowForm(false);
  };

  const getReportTitle = () => {
    switch (selectedTable) {
      case 1:
        return 'Báo cáo chi tiết tình hình tăng giảm TSCĐ theo bộ phận';
      case 2:
        return 'Báo cáo chi tiết tình hình tăng giảm TSCĐ theo loại TSCĐ';
      default:
        return 'Báo cáo chi tiết tình hình tăng giảm TSCĐ';
    }
  };

  let tables = [
    {
      name: getReportTitle(),
      rows: rows,
      columns: assetDetailIncreaseDecreaseReportColumns(handleOpenViewForm, handleOpenEditForm)
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoPopupForm<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Báo cáo chi tiết tình hình tăng giảm TSCĐ'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={assetDetailIncreaseDecreaseSchema}
        headerFields={<FilterTab formMode={formMode} />}
        maxWidth='md'
        fullWidth={true}
        tabs={[
          {
            id: 'detail',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} />
          },
          {
            id: 'other',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        onDeleteClick={() => {}}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
        data={rows}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
