import { Eye, FileText, Lock, Pencil, Plus, RefreshCw, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoMenuButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  isEditDisabled?: boolean;
  onViewClick: () => void;
  isViewDisabled?: boolean;
  onDeleteClick: () => void;
}

export const ActionBar = ({
  onAddClick,
  onEditClick,
  isEditDisabled = true,
  onViewClick,
  isViewDisabled = true,
  onDeleteClick
}: ActionBarProps) => (
  <AritoActionBar titleComponent={<h1 className='text-xl font-bold'><PERSON><PERSON><PERSON><PERSON> chuyển bộ phận sử dụng TSCĐ</h1>}>
    <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
    <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} disabled={isEditDisabled} />
    <AritoActionButton title='Xoá' icon={Trash} onClick={onDeleteClick} />
    <AritoActionButton title='Sao chép' icon={FileText} onClick={() => {}} />
    <AritoActionButton title='Cố định cột' icon={Lock} onClick={() => {}} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} />
    <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} disabled={isViewDisabled} />
    <AritoMenuButton
      title='Khác'
      items={[
        {
          title: 'Kết xuất dữ liệu',
          icon: <AritoIcon icon={555} />,
          onClick: () => {},
          group: 0
        }
      ]}
    />
  </AritoActionBar>
);
