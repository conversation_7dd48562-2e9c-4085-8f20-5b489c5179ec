import { Check, Lock, RefreshCw, Search, Trash } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

interface ActionBarProps {
  onViewClick: () => void;
  isViewDisabled?: boolean;
}

export const ActionBar = ({ onViewClick, isViewDisabled = true }: ActionBarProps) => (
  <AritoActionBar
    titleComponent={<h1 className='text-xl font-bold'>Tính chi phí khấu hao và hạch toán tài sản cố định</h1>}
  >
    <AritoActionButton title='Tính lại' icon={Check} onClick={() => {}} />
    <AritoActionButton title='Xoá & tính' icon={Trash} onClick={() => {}} />
    <AritoActionButton title='Lọc' icon={Search} onClick={() => {}} />
    <AritoActionButton title='Refresh' icon={RefreshCw} onClick={() => {}} />
    <AritoActionButton title='Cố định cột' icon={Lock} onClick={() => {}} />
  </AritoActionBar>
);
