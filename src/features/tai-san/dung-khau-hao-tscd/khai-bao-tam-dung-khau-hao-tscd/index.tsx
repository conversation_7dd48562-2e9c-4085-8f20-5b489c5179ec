'use client';

import React from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { StopDepreciationColumns } from './cols-definition';
import { FormDialog, ActionBar } from './components';
import { useFormState } from './hooks/useFormState';
import { useData } from './hooks/useData';

export default function KhaiBaoTamDungKhauHaoTSCD({ initialRows }: { initialRows: any[] }) {
  const {
    showForm,
    formMode,
    handleCloseForm,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleSearchClick
  } = useFormState();

  const { rows, selectedRowIndex, handleRowClick, handleFormSubmit, handleRefreshClick, handleFixedColumnsClick } =
    useData(initialRows);

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: StopDepreciationColumns
    }
  ];

  return (
    <div className='flex h-screen flex-col lg:overflow-hidden'>
      {showForm && (
        <FormDialog
          open={showForm}
          onClose={handleCloseForm}
          onSubmit={handleFormSubmit}
          onAdd={handleAddClick}
          onEdit={handleEditClick}
          onDelete={handleDeleteClick}
          onCopy={handleCopyClick}
          formMode={formMode}
        />
      )}

      <ActionBar
        onAddClick={handleAddClick}
        onEditClick={handleEditClick}
        onDeleteClick={handleDeleteClick}
        onCopyClick={handleCopyClick}
        onViewClick={handleViewClick}
        onSearchClick={handleSearchClick}
        onFixedColumnsClick={handleFixedColumnsClick}
        onRefreshClick={handleRefreshClick}
      />

      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
