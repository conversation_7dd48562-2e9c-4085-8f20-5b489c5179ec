import { exportRequestDetailItemColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface ExportRequestDetailItemsTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const ExportRequestDetailItemsTab = ({ value, onChange, formMode }: ExportRequestDetailItemsTabProps) => {
  return (
    <div className='h-[270px] w-screen'>
      <AritoInputTable value={value} columns={exportRequestDetailItemColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
