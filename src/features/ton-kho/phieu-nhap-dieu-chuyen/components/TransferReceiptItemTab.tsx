import { transferReceiptItemColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface TransferReceiptItemTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const TransferReceiptItemTab = ({ value, onChange, formMode }: TransferReceiptItemTabProps) => {
  return (
    <div className='h-[270px] w-screen'>
      <AritoInputTable value={value} columns={transferReceiptItemColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
