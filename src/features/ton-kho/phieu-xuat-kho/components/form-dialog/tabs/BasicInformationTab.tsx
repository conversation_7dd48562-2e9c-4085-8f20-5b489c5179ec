import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { khachHangSearchColumns, quyenChungTuSearchColumns } from '@/constants';
import { FormField } from '@/components/custom/arito/form/form-field';
import { useNgoaiTe } from '@/hooks/queries/useNgoaiTe';
import { QUERY_KEYS } from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInformationTabProps {
  formMode: FormMode;
  selectedCustomer?: any;
  selectedDocumentBook?: any;
  handleCustomerSelection: (customer: any) => void;
  handleDocumentBookSelection: (documentBook: any) => void;
}

export const BasicInformationTab = ({
  formMode,
  selectedCustomer,
  selectedDocumentBook,
  handleCustomerSelection,
  handleDocumentBookSelection
}: BasicInformationTabProps) => {
  const isViewMode = formMode === 'view';

  const { currencies } = useNgoaiTe();

  // Convert currencies to options for FormField (using UUID as value)
  const ngoaiTeOptions = currencies.map(currency => ({
    value: currency.uuid || currency.ma_nt,
    label: currency.ma_nt
  }));

  return (
    <div className='space-y-2 p-4'>
      <div className='grid grid-cols-1 gap-x-6 space-y-2 lg:grid-cols-4 lg:space-y-0'>
        <div className='col-span-3'>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Giao dịch</Label>
            <div className='w-[250px]'>
              <FormField
                type='select'
                name='ma_gd'
                disabled={formMode === 'view'}
                options={[
                  { value: 'NB', label: 'NB. Xuất nội bộ' },
                  { value: 'DC', label: 'DC. Điều chỉnh' },
                  { value: 'XK', label: 'SK. Xuất khác' },
                  { value: 'X1', label: 'X1. Xuất nguyên vật liệu' },
                  { value: 'TS', label: 'TS. Mua tài sản/công cụ' }
                ]}
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Mã đối tượng</Label>
            <div className='w-[57.5%]'>
              <SearchField<any>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}`}
                searchColumns={khachHangSearchColumns}
                dialogTitle='Danh mục đối tượng'
                columnDisplay='customer_code'
                displayRelatedField='customer_name'
                value={selectedCustomer?.customer_code || ''}
                relatedFieldValue={selectedCustomer?.customer_name || ''}
                onRowSelection={handleCustomerSelection}
                disabled={isViewMode}
                className='w-full'
                classNameRelatedField='w-auto min-w-[300px] max-w-full'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Diễn giải</Label>
            <div className='w-full'>
              <FormField type='text' name='dien_giai' disabled={formMode === 'view'} />
            </div>
          </div>
        </div>

        <div>
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Số chứng từ</Label>
            <div className='w-[57.5%]'>
              <SearchField<any>
                type='text'
                searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}`}
                searchColumns={quyenChungTuSearchColumns}
                dialogTitle='Danh mục quyển sổ'
                columnDisplay='ma_nk'
                displayRelatedField='ten_nk'
                value={selectedDocumentBook?.ma_nk || selectedDocumentBook?.ma_ct || ''}
                relatedFieldValue={selectedDocumentBook?.ten_nk || selectedDocumentBook?.ten_ct || ''}
                onRowSelection={handleDocumentBookSelection}
                disabled={isViewMode}
                className='w-full'
                classNameRelatedField='w-auto min-w-[300px] max-w-full'
              />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Ngày chứng từ</Label>
            <div className='w-[130px]'>
              <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
            </div>
          </div>

          <div className='flex items-center'>
            <div className='flex items-center'>
              <Label className='w-32 min-w-32'>Ngoại tệ</Label>
              <div className='w-[100px]'>
                <FormField type='select' name='ma_nt' disabled={formMode === 'view'} options={ngoaiTeOptions} />
              </div>
            </div>

            <div className='mb-2 ml-2 w-[100px]'>
              <FormField type='number' name='ty_gia' disabled={formMode === 'view'} />
            </div>
          </div>

          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Trạng thái</Label>
            <div className='w-[210px]'>
              <FormField
                type='select'
                name='status'
                disabled={formMode === 'view'}
                options={[
                  { value: '0', label: 'Lập chứng từ' },
                  { value: '3', label: 'Chờ duyệt' },
                  { value: '5', label: 'Xuất kho' },
                  { value: '6', label: 'Duyệt phiếu yêu cầu xuất kho' }
                ]}
              />
            </div>
          </div>

          <div className='mt-2 flex items-center'>
            <Label className='w-32 min-w-32'></Label>
            <FormField
              type='checkbox'
              label='Dữ liệu được nhận'
              name='da_ghi_so'
              disabled={formMode === 'view'}
              className='flex items-center'
            />
          </div>
        </div>
      </div>
    </div>
  );
};
