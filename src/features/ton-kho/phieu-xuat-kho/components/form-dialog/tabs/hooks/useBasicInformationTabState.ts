import { useFormContext } from 'react-hook-form';
import { useState, useEffect } from 'react';

interface UseBasicInformationTabStateProps {
  formMode: 'add' | 'edit' | 'view' | 'copy' | 'search';
  selectedObj?: any;
}

export const useBasicInformationTabState = ({ formMode, selectedObj }: UseBasicInformationTabStateProps) => {
  const { getValues } = useFormContext();
  const [selectedCustomer, setSelectedCustomer] = useState<any | null>(null);
  const [selectedDocumentBook, setSelectedDocumentBook] = useState<any | null>(null);

  const normalizeCustomerData = (customerData: any) => {
    if (!customerData) return null;

    if (customerData.customer_code || customerData.customer_name) {
      return customerData;
    }

    if (customerData.ma_kh || customerData.ten_kh) {
      return {
        ...customerData,
        customer_code: customerData.ma_kh,
        customer_name: customerData.ten_kh
      };
    }

    return customerData;
  };

  // Initialize state when selectedObj changes (for copy/edit mode)
  useEffect(() => {
    if (selectedObj && (formMode === 'edit' || formMode === 'view')) {
      if (selectedObj.khach_hang_data) {
        setSelectedCustomer(normalizeCustomerData(selectedObj.khach_hang_data));
      } else if (selectedObj.ma_kh_data) {
        setSelectedCustomer(normalizeCustomerData(selectedObj.ma_kh_data));
      }

      // Initialize document book data if available (check multiple possible field names)
      if (selectedObj.quyen_chung_tu_data) {
        setSelectedDocumentBook(selectedObj.quyen_chung_tu_data);
      } else if (selectedObj.ma_nk_data) {
        setSelectedDocumentBook(selectedObj.ma_nk_data);
      } else if (selectedObj.so_ct_data) {
        setSelectedDocumentBook(selectedObj.so_ct_data);
      }
    }
  }, [selectedObj, formMode]);

  // Load data from form values when in edit mode (similar to nha-cung-cap pattern)
  useEffect(() => {
    if (formMode === 'edit') {
      const values = getValues();
      if (values && Object.keys(values).length > 0) {
        // Load customer data (check multiple possible field names)
        if ((values as any).khach_hang_data) {
          setSelectedCustomer(normalizeCustomerData((values as any).khach_hang_data));
        } else if ((values as any).ma_kh_data) {
          setSelectedCustomer(normalizeCustomerData((values as any).ma_kh_data));
        }

        // Load document book data (check multiple possible field names)
        if ((values as any).quyen_chung_tu_data) {
          setSelectedDocumentBook((values as any).quyen_chung_tu_data);
        } else if ((values as any).ma_nk_data) {
          setSelectedDocumentBook((values as any).ma_nk_data);
        } else if ((values as any).so_ct_data) {
          setSelectedDocumentBook((values as any).so_ct_data);
        }
      }
    }
  }, [formMode, getValues]);

  // Load data from selectedObj when copying (formMode === 'add' with selectedObj)
  useEffect(() => {
    if (formMode === 'add' && selectedObj) {
      // Initialize customer data if available (check multiple possible field names)
      if (selectedObj.khach_hang_data) {
        setSelectedCustomer(normalizeCustomerData(selectedObj.khach_hang_data));
      } else if (selectedObj.ma_kh_data) {
        setSelectedCustomer(normalizeCustomerData(selectedObj.ma_kh_data));
      }

      // Initialize document book data if available (check multiple possible field names)
      if (selectedObj.quyen_chung_tu_data) {
        setSelectedDocumentBook(selectedObj.quyen_chung_tu_data);
      } else if (selectedObj.ma_nk_data) {
        setSelectedDocumentBook(selectedObj.ma_nk_data);
      } else if (selectedObj.so_ct_data) {
        setSelectedDocumentBook(selectedObj.so_ct_data);
      }
    }
  }, [formMode, selectedObj]);

  const handleCustomerSelection = (customer: any) => {
    setSelectedCustomer(customer);
  };

  const handleDocumentBookSelection = (documentBook: any) => {
    setSelectedDocumentBook(documentBook);
  };

  return {
    selectedCustomer,
    selectedDocumentBook,
    handleCustomerSelection,
    handleDocumentBookSelection
  };
};
