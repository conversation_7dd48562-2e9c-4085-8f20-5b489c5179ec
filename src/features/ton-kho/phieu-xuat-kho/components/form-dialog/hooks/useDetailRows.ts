import { GridCellParams } from '@mui/x-data-grid';
import { useState } from 'react';
import { ChiTietPhieuXuatKho } from '@/types/schemas/phieu-xuat-kho.type';

export interface SelectedCellInfo {
  id: string;
  field: string;
}

export interface UseDetailRowsReturn {
  rows: ChiTietPhieuXuatKho[];
  setRows: React.Dispatch<React.SetStateAction<ChiTietPhieuXuatKho[]>>;
  selectedRowUuid: string | null;

  selectedRow: ChiTietPhieuXuatKho | null;
  selectedCell: SelectedCellInfo | null;
  handleRowClick: (params: { id: string; row: ChiTietPhieuXuatKho }) => void;
  handleCellClick: (params: GridCellParams) => void;
  clearSelection: () => void;
  handleAddRow: () => void;
  handleDeleteRow: () => void;
  handleCopyRow: () => void;
  handlePasteRow: () => void;
  handleMoveRow: (direction: 'up' | 'down') => void;
  handleCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
  handleExport: () => void;
  handlePin: () => void;
}

// Helper function to transform API data to component format
const transformApiDataToComponentFormat = (apiData: any[]): ChiTietPhieuXuatKho[] => {
  if (!apiData || apiData.length === 0) {
    return [
      {
        uuid: String(Math.random()),
        so_luong: 0,
        don_gia: 0,
        thanh_tien: 0,
        ma_vt: '',
        ten_vt: '',
        ma_dvt: '',
        ma_kho: '',
        ton_kho: 0
      }
    ];
  }

  return apiData.map((item, index) => {
    // Check if it's already in the correct format (has ma_vt, so_luong, etc.)
    if (item.ma_vt || item.so_luong !== undefined || item.don_gia !== undefined) {
      return { ...item, uuid: item.uuid || String(Math.random()) };
    }

    // If it's API format (ma_ct_code, ma_ct_name, etc.), map real data from document book details
    return {
      uuid: item.uuid || String(Math.random()),
      ma_vt: item.ma_ct_code || '', // Use ma_ct_code as product code
      ten_vt: item.ma_ct_name || '', // Use ma_ct_name as product name
      ma_dvt: 'CAI', // Default unit
      ma_kho: 'KHO01', // Default warehouse
      ton_kho: 0, // Default stock
      so_luong: 1, // Default quantity
      don_gia: 0, // Default price
      thanh_tien: 0, // Default total
      don_gia_vnd: 0,
      thanh_tien_vnd: 0,
      dich_danh: false,
      tk_co: '',
      tk_no: '',
      line: item.line || index + 1, // Use line number from API
      // Related data objects for SearchField compatibility
      vat_tu_data: {
        ma_vt: item.ma_ct_code || '',
        ten_vt: item.ma_ct_name || ''
      },
      kho_data: {
        ma_kho: 'KHO01',
        ten_kho: 'Kho chính'
      },
      // Keep original data for reference
      _originalData: item
    } as ChiTietPhieuXuatKho;
  });
};

export default function useDetailRows(initialData: any[] = []): UseDetailRowsReturn {
  // Initialize with transformed data - similar to useStageRows pattern
  const initialTransformedData = transformApiDataToComponentFormat(initialData);
  const [rows, setRows] = useState<ChiTietPhieuXuatKho[]>(initialTransformedData);
  const [selectedRowUuid, setSelectedRowUuid] = useState<string | null>(null);
  const [selectedRow, setSelectedRow] = useState<ChiTietPhieuXuatKho | null>(null);
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(null);

  const handleAddRow = () => {
    const newRow: ChiTietPhieuXuatKho = {
      uuid: String(Math.random()),
      ma_vt: '',
      ten_vt: '',
      ma_dvt: '',
      ma_kho: '',
      ton_kho: 0,
      so_luong: 0,
      don_gia: 0,
      thanh_tien: 0,
      don_gia_vnd: 0,
      thanh_tien_vnd: 0,
      dich_danh: false,
      tk_co: '',
      tk_no: '',
      line: rows.length + 1
    };

    setRows([...rows, newRow]);

    // Select the newly added row
    setSelectedRowUuid(newRow.uuid!);
    setSelectedRow(newRow);
  };

  const handleDeleteRow = () => {
    let updatedRows: ChiTietPhieuXuatKho[] = [];

    if (selectedRowUuid) {
      updatedRows = rows.filter(row => row.uuid !== selectedRowUuid);
      setRows(updatedRows);
    } else if (rows.length > 0) {
      updatedRows = rows.slice(0, -1);
      setRows(updatedRows);
    }

    // If there are remaining rows, select the last one
    if (updatedRows.length > 0) {
      const lastRow = updatedRows[updatedRows.length - 1];
      setSelectedRowUuid(lastRow.uuid!);
      setSelectedRow(lastRow);
    } else {
      // If no rows remain, clear selection
      clearSelection();
    }
  };

  const handleCopyRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    const newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    setRows([...rows, newRow]);

    // Select the newly copied row
    setSelectedRowUuid(newRow.uuid!);
    setSelectedRow(newRow);
  };

  const handlePasteRow = () => {
    if (!selectedRow) return;

    const { uuid, ...rowDataWithoutUuid } = selectedRow;

    let newRow = {
      ...rowDataWithoutUuid,
      uuid: String(Math.random())
    };

    if (selectedRowUuid) {
      const selectedIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
      if (selectedIndex === -1) return;

      const newRows = [...rows];
      newRows.splice(selectedIndex + 1, 0, newRow);

      setRows(newRows);
    } else {
      setRows([...rows, newRow]);
    }

    // Select the newly pasted row
    setSelectedRowUuid(newRow.uuid!);
    setSelectedRow(newRow);
  };

  const handleMoveRow = (direction: 'up' | 'down') => {
    if (!selectedRowUuid) return;

    const currentIndex = rows.findIndex(row => row.uuid === selectedRowUuid);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(rows.length - 1, currentIndex + 1);

    if (newIndex === currentIndex) return;

    const newRows = [...rows];
    const [movedRow] = newRows.splice(currentIndex, 1);
    newRows.splice(newIndex, 0, movedRow);

    setRows(newRows);

    // Ensure selection follows the moved row
    setSelectedRow(movedRow);
  };

  const handleCellValueChange = (rowUuid: string, field: string, newValue: any) => {
    const rowIndex = rows.findIndex(row => row.uuid === rowUuid);
    if (rowIndex === -1) return;

    const updatedRows = [...rows];
    const currentRow = updatedRows[rowIndex];

    if (field === 'vat_tu_data' && typeof newValue === 'object' && newValue !== null) {
      // Auto-fill related fields when selecting product
      updatedRows[rowIndex] = {
        ...currentRow,
        ma_vt: newValue.uuid || newValue.ma_vt, // Use UUID for backend
        ten_vt: newValue.ten_vt || newValue.name,
        ma_dvt: newValue.don_vi_tinh_data?.uuid || newValue.ma_dvt || currentRow.ma_dvt, // Use UUID for unit
        don_gia: newValue.don_gia || currentRow.don_gia,
        don_gia_vnd: newValue.don_gia_vnd || currentRow.don_gia_vnd,
        tk_co: newValue.tk_co || currentRow.tk_co,
        tk_no: newValue.tk_no || currentRow.tk_no,
        vat_tu_data: newValue,
        // Auto-fill unit data if available
        don_vi_tinh_data: newValue.don_vi_tinh_data || currentRow.don_vi_tinh_data
      };
      console.log('Auto-filled product data:', updatedRows[rowIndex]); // Debug log
    } else if (field === 'kho_data' && typeof newValue === 'object' && newValue !== null) {
      updatedRows[rowIndex] = {
        ...currentRow,
        ma_kho: newValue.uuid || newValue.ma_kho, // Use UUID for backend
        kho_data: newValue
      };
    } else if (field === 'ly_do_xuat_data' && typeof newValue === 'object' && newValue !== null) {
      updatedRows[rowIndex] = {
        ...currentRow,
        ly_do_xuat: newValue.uuid || newValue.ma_ly_do, // Use UUID for backend
        ly_do_xuat_data: newValue
      };
    } else if (field === 'tai_khoan_no_data' && typeof newValue === 'object' && newValue !== null) {
      updatedRows[rowIndex] = {
        ...currentRow,
        tk_no: newValue.uuid || newValue.ma_tk, // Use UUID for backend
        tai_khoan_no_data: newValue
      };
    } else if (field === 'bo_phan_data' && typeof newValue === 'object' && newValue !== null) {
      updatedRows[rowIndex] = {
        ...currentRow,
        ma_bp: newValue.uuid || newValue.ma_bp, // Use UUID for backend
        bo_phan_data: newValue
      };
    } else if (field === 'don_vi_tinh_data' && typeof newValue === 'object' && newValue !== null) {
      updatedRows[rowIndex] = {
        ...currentRow,
        ma_dvt: newValue.uuid || newValue.ma_dvt, // Use UUID for backend
        don_vi_tinh_data: newValue
      };
    } else if (field === 'so_luong' || field === 'don_gia') {
      const so_luong = field === 'so_luong' ? newValue : currentRow.so_luong || 0;
      const don_gia = field === 'don_gia' ? newValue : currentRow.don_gia || 0;
      const thanh_tien = so_luong * don_gia;

      updatedRows[rowIndex] = {
        ...currentRow,
        [field]: newValue,
        thanh_tien,
        thanh_tien_vnd: thanh_tien
      };
    } else {
      updatedRows[rowIndex] = {
        ...currentRow,
        [field]: newValue
      };
    }

    setRows(updatedRows);

    // If the changed row is the currently selected row, update selectedRow
    if (rowUuid === selectedRowUuid) {
      setSelectedRow(updatedRows[rowIndex]);
    }
  };

  const handleRowClick = (params: { id: string; row: ChiTietPhieuXuatKho }) => {
    const rowUuid = params.id || params.row?.uuid;
    if (!rowUuid) return;

    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row);
  };

  const handleCellClick = (params: GridCellParams) => {
    const rowUuid = params.id.toString();
    if (!rowUuid) return;

    // Update row selection
    setSelectedRowUuid(rowUuid);
    setSelectedRow(params.row as ChiTietPhieuXuatKho);

    // Update cell selection
    setSelectedCell({
      id: rowUuid,
      field: params.field
    });
  };

  const clearSelection = () => {
    setSelectedRowUuid(null);
    setSelectedRow(null);
    setSelectedCell(null);
  };

  const handleExport = () => {
    console.log('Export detail rows:', rows);
  };

  const handlePin = () => {
    console.log('Pin detail rows:', rows);
  };

  return {
    rows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange,
    handleExport,
    handlePin
  };
}
