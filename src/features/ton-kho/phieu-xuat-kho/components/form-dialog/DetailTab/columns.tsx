import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  vatTuSearchColumns,
  warehouseSearchColumns,
  accountSearchColumns,
  boPhanSearchColumns,
  vuViecSearchColumns,
  phiSearchColumns,
  lenhSanXuatSearchColumns,
  hopDongSearchColumns,
  exportReasonSearchColumns,
  paymentInstallmentSearchColumns,
  invalidExpenseSearchColumns,
  donViSearchColumns
} from '@/constants/search-columns';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { QUERY_KEYS } from '@/constants/query-keys';

export const getDetailColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => {
  console.log('getDetailColumns called'); // Debug log
  return [
    // Mã sản phẩm - SearchField với auto-fill tên sản phẩm
    {
      field: 'ma_vt',
      headerName: 'Mã sản phẩm',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
          searchColumns={vatTuSearchColumns}
          dialogTitle='Danh mục vật tư'
          columnDisplay='ma_vt'
          displayRelatedField='ten_vt'
          value={params.row.vat_tu_data?.ma_vt || params.row.ma_vt || ''}
          relatedFieldValue={params.row.vat_tu_data?.ten_vt || params.row.ten_vt || ''}
          onRowSelection={row => {
            console.log('Product selected:', row); // Debug log
            onCellValueChange(params.row.uuid, 'vat_tu_data', row);
          }}
        />
      )
    },
    // Tên sản phẩm - Auto-filled từ mã sản phẩm
    {
      field: 'ten_vt',
      headerName: 'Tên sản phẩm',
      width: 200,
      renderCell: (params: GridRenderCellParams) => (
        <CellField
          name='ten_vt'
          type='text'
          value={params?.row.vat_tu_data?.ten_vt || params?.row.ten_vt || ''}
          disabled
        />
      )
    },
    // Đơn vị tính - SearchField (khóa ngoại)
    {
      field: 'ma_dvt',
      headerName: 'Đvt',
      width: 80,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DON_VI_TINH}`}
          searchColumns={donViSearchColumns}
          dialogTitle='Danh mục đơn vị tính'
          columnDisplay='ma_dvt'
          displayRelatedField='ten_dvt'
          value={params.row.don_vi_tinh_data?.ma_dvt || params.row.ma_dvt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'don_vi_tinh_data', row);
          }}
        />
      )
    },
    // Mã kho - SearchField
    {
      field: 'ma_kho',
      headerName: 'Mã kho',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHO_HANG}`}
          searchColumns={warehouseSearchColumns}
          dialogTitle='Danh mục kho'
          columnDisplay='ma_kho'
          displayRelatedField='ten_kho'
          value={params.row.kho_data?.ma_kho || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'kho_data', row);
          }}
        />
      )
    },
    // Tồn - FormField (only read)
    {
      field: 'ton_kho',
      headerName: 'Tồn',
      width: 100,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='ton_kho' type='number' value={params.value || 0} disabled />
      )
    },
    // Số lượng - FormField kiểu số
    {
      field: 'so_luong',
      headerName: 'Số lượng',
      width: 100,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField
          name='so_luong'
          type='number'
          value={params.value || 0}
          onChange={value => {
            onCellValueChange(params.row.uuid, 'so_luong', value);
            // Auto-calculate thành tiền khi số lượng thay đổi
            const donGia = Number(params.row.don_gia) || 0;
            const thanhTien = Number(value) * donGia;
            onCellValueChange(params.row.uuid, 'thanh_tien', thanhTien);
            onCellValueChange(params.row.uuid, 'thanh_tien_vnd', thanhTien);
          }}
        />
      )
    },
    // Đơn giá - FormField kiểu số
    {
      field: 'don_gia',
      headerName: 'Đơn giá',
      width: 120,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField
          name='don_gia'
          type='number'
          value={params.value || 0}
          onChange={value => {
            onCellValueChange(params.row.uuid, 'don_gia', value);
            // Auto-calculate thành tiền khi đơn giá thay đổi
            const soLuong = Number(params.row.so_luong) || 0;
            const thanhTien = Number(value) * soLuong;
            onCellValueChange(params.row.uuid, 'thanh_tien', thanhTien);
            onCellValueChange(params.row.uuid, 'thanh_tien_vnd', thanhTien);
          }}
        />
      )
    },
    // Thành tiền - Auto-calculated (readonly)
    {
      field: 'thanh_tien',
      headerName: 'Thành tiền',
      width: 120,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='thanh_tien' type='number' value={params.value || 0} disabled />
      )
    },
    // Đích danh - Checkbox
    {
      field: 'dich_danh',
      headerName: 'Đích danh',
      width: 100,
      type: 'boolean',
      renderCell: (params: GridRenderCellParams) => (
        <CellField
          name='dich_danh'
          type='checkbox'
          value={params.value || false}
          className='text-center'
          onChange={value => onCellValueChange(params.row.uuid, 'dich_danh', value)}
        />
      )
    },
    // Giá VND - only read
    {
      field: 'don_gia_vnd',
      headerName: 'Giá VND',
      width: 120,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='don_gia_vnd' type='number' value={params.value || 0} disabled />
      )
    },
    // Tiền VND - only read
    {
      field: 'thanh_tien_vnd',
      headerName: 'Tiền VND',
      width: 120,
      type: 'number',
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='thanh_tien_vnd' type='number' value={params.value || 0} disabled />
      )
    },
    // TK có - only read
    {
      field: 'tk_co',
      headerName: 'TK có',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='tk_co' type='text' value={params.value || ''} disabled />
      )
    },
    // Lý do xuất - SearchField với DANH_MUC_NHAP_XUAT
    {
      field: 'ly_do_xuat',
      headerName: 'Lý do xuất',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DANH_MUC_NHAP_XUAT}`}
          searchColumns={exportReasonSearchColumns}
          dialogTitle='Danh mục nhập xuất'
          columnDisplay='ma_ly_do'
          displayRelatedField='ten_ly_do'
          value={params.row.ly_do_xuat_data?.ma_ly_do || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'ly_do_xuat_data', row);
          }}
        />
      )
    },
    // TK nợ - SearchField với TAI_KHOAN
    {
      field: 'tk_no',
      headerName: 'TK nợ',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
          searchColumns={accountSearchColumns}
          dialogTitle='Danh mục tài khoản'
          columnDisplay='code'
          displayRelatedField='name'
          value={params.row.tai_khoan_no_data?.ma_tk || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'tai_khoan_no_data', row);
          }}
        />
      )
    },
    // Bộ phận - SearchField với BO_PHAN
    {
      field: 'ma_bp',
      headerName: 'Bộ phận',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
          searchColumns={boPhanSearchColumns}
          dialogTitle='Danh mục bộ phận'
          columnDisplay='ma_bp'
          displayRelatedField='ten_bp'
          value={params.row.bo_phan_data?.ma_bp || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'bo_phan_data', row);
          }}
        />
      )
    },
    // Vụ việc - SearchField với VU_VIEC
    {
      field: 'ma_vu_viec',
      headerName: 'Vụ việc',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
          searchColumns={vuViecSearchColumns}
          dialogTitle='Danh mục vụ việc'
          columnDisplay='ma_vv'
          displayRelatedField='ten_vv'
          value={params.row.vu_viec_data?.ma_vu_viec || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'vu_viec_data', row);
          }}
        />
      )
    },
    // Đợt thanh toán - SearchField với DOT_THANH_TOAN
    {
      field: 'dot_tt',
      headerName: 'Đợt thanh toán',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}`}
          searchColumns={paymentInstallmentSearchColumns}
          dialogTitle='Đợt thanh toán'
          columnDisplay='ma_dot_tt'
          displayRelatedField='ten_dot_tt'
          value={params.row.dot_thanh_toan_data?.ma_dot_tt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'dot_thanh_toan_data', row);
          }}
        />
      )
    },
    // Khế ước - SearchField với KHE_UOC
    {
      field: 'ma_hd',
      headerName: 'Khế ước',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
          searchColumns={hopDongSearchColumns}
          dialogTitle='Danh mục khế ước'
          columnDisplay='ma_hd'
          displayRelatedField='ten_hd'
          value={params.row.khe_uoc_data?.ma_khe_uoc || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'khe_uoc_data', row);
          }}
        />
      )
    },
    // Phí - SearchField với PHI
    {
      field: 'phi',
      headerName: 'Phí',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.PHI}`}
          searchColumns={phiSearchColumns}
          dialogTitle='Danh mục phí'
          columnDisplay='ma_phi'
          displayRelatedField='ten_phi'
          value={params.row.phi_data?.ma_phi || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'phi_data', row);
          }}
        />
      )
    },
    // Sản phẩm - SearchField với VAT_TU
    {
      field: 'ma_sp',
      headerName: 'Sản phẩm',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
          searchColumns={vatTuSearchColumns}
          dialogTitle='Danh mục sản phẩm'
          columnDisplay='ma_vt'
          displayRelatedField='ten_vt'
          value={params.row.san_pham_data?.ma_vt || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'san_pham_data', row);
          }}
        />
      )
    },
    // Lệnh sản xuất - SearchField với LENH_SAN_XUAT
    {
      field: 'ma_lenh_sx',
      headerName: 'Lệnh sản xuất',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}`}
          searchColumns={lenhSanXuatSearchColumns}
          dialogTitle='Lệnh sản xuất'
          columnDisplay='so_lsx'
          displayRelatedField='description'
          value={params.row.lenh_san_xuat_data?.ma_lenh_sx || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'lenh_san_xuat_data', row);
          }}
        />
      )
    },
    // C/p không h/lệ - SearchField với CHI_PHI_KHONG_HOP_LE
    {
      field: 'chi_phi_khong_hop_le',
      headerName: 'C/p không h/lệ',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <SearchField<any>
          type='text'
          searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}`}
          searchColumns={invalidExpenseSearchColumns}
          dialogTitle='Chi phí không hợp lệ'
          columnDisplay='ma_chi_phi'
          displayRelatedField='ten_chi_phi'
          value={params.row.chi_phi_khong_hop_le_data?.ma_chi_phi || ''}
          onRowSelection={row => {
            onCellValueChange(params.row.uuid, 'chi_phi_khong_hop_le_data', row);
          }}
        />
      )
    },
    // Số phiếu YC - only read
    {
      field: 'so_phieu_yc',
      headerName: 'Số phiếu YC',
      width: 120,
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='so_phieu_yc' type='text' value={params.value || ''} disabled />
      )
    },
    // Dòng YC - only read
    {
      field: 'dong_yc',
      headerName: 'Dòng YC',
      width: 100,
      renderCell: (params: GridRenderCellParams) => (
        <CellField name='dong_yc' type='text' value={params.value || ''} disabled />
      )
    }
  ];
};
