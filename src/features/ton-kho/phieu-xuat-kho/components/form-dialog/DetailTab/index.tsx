import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from './action';

import { GridCellParams } from '@mui/x-data-grid';

import { getDetailColumns } from './columns';
import { FormMode } from '@/types/form';

interface SelectedCellInfo {
  id: string;
  field: string;
}

interface DetailTabProps {
  formMode: FormMode;
  rows: any[]; // Remove optional and default value
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow?: () => void;
  onDeleteRow?: () => void;
  onCopyRow?: () => void;
  onPasteRow?: () => void;
  onMoveRow?: (direction: 'up' | 'down') => void;
  onExport?: () => void;
  onPin?: () => void;
  onCellValueChange?: (rowUuid: string, field: string, newValue: any) => void;
}

export const DetailTab: React.FC<DetailTabProps> = ({
  formMode,
  rows, // Remove default value
  selectedRowUuid,
  selectedCell,
  onRowClick,
  onCellClick,
  onAddRow = () => {},
  onDeleteRow = () => {},
  onCopyRow = () => {},
  onPasteRow = () => {},
  onMoveRow = () => {},
  onExport = () => {},
  onPin = () => {},
  onCellValueChange = () => {}
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      onCellClick={onCellClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDetailColumns(onCellValueChange)}
      getRowId={row => {
        return row?.uuid || '';
      }}
      actionButtons={
        <InputTableActionBar
          mode={formMode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={onExport}
          handlePin={onPin}
        />
      }
    />
  );
};
