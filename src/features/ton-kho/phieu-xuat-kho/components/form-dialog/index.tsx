import React, { useState } from 'react';
import { useBasicInformationTabState } from './tabs/hooks/useBasicInformationTabState';
import { BasicInformationTab, DetailTab, FileTab } from './tabs';
import { AritoForm } from '@/components/custom/arito/form';
import ConfirmDialog from '../ConfirmDialog';
import { FormValues } from '../../schema';
import { FormMode } from '@/types/form';
import { useDetailRows } from './hooks';
import BottomBar from '../BottomBar';

interface FormDialogProps {
  formMode: FormMode;
  initialData?: FormValues;
  selectedObj?: any;
  onSubmit: (data: FormValues) => void;
  onClose: () => void;
}

// Wrapper component to use hook inside FormProvider
const BasicInformationTabWrapper = ({ formMode, selectedObj }: { formMode: FormMode; selectedObj?: any }) => {
  const basicInformationTabState = useBasicInformationTabState({ formMode, selectedObj });
  return <BasicInformationTab formMode={formMode} {...basicInformationTabState} />;
};

const FormDialog = ({ initialData, selectedObj, onClose, onSubmit, formMode }: FormDialogProps) => {
  const detailData = (() => {
    if (formMode === 'edit' && selectedObj) {
      const data = (selectedObj as any).chi_tiet || [];
      return data;
    }
    const data = (initialData as any)?.chi_tiet || [];
    return data;
  })();

  // BasicInformationTab state will be managed inside AritoForm

  const {
    rows: detailRows,
    setRows,
    selectedRowUuid,
    selectedRow,
    selectedCell,
    handleRowClick,
    handleCellClick,
    clearSelection,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange,
    handleExport,
    handlePin
  } = useDetailRows(detailData);
  const [isConfirm, setIsConfirm] = useState(false);

  const handleSubmit = (data: FormValues) => {
    // Transform detail rows to match backend model fields
    const transformedDetailRows = detailRows.map((row, index) => {
      const transformedRow: any = {
        line: row.line || index + 1,

        ma_vt: row.ma_vt && row.ma_vt !== '' ? row.ma_vt : null,
        dvt: row.ma_dvt && row.ma_dvt !== '' ? row.ma_dvt : null, // Map ma_dvt -> dvt
        ma_kho: row.ma_kho && row.ma_kho !== '' ? row.ma_kho : null,

        // Optional foreign keys
        ma_bp: row.ma_bp && row.ma_bp !== '' ? row.ma_bp : null,
        ma_vv: row.ma_vu_viec && row.ma_vu_viec !== '' ? row.ma_vu_viec : null, // Map ma_vu_viec -> ma_vv
        ma_hd: row.ma_hd && row.ma_hd !== '' ? row.ma_hd : null,
        ma_dtt: row.dot_tt && row.dot_tt !== '' ? row.dot_tt : null, // Map dot_tt -> ma_dtt
        ma_ku: (row as any).ma_khe_uoc && (row as any).ma_khe_uoc !== '' ? (row as any).ma_khe_uoc : null, // Map ma_khe_uoc -> ma_ku
        ma_phi: (row as any).phi && (row as any).phi !== '' ? (row as any).phi : null,
        ma_sp: row.ma_sp && row.ma_sp !== '' ? row.ma_sp : null,
        ma_cp0:
          (row as any).chi_phi_khong_hop_le && (row as any).chi_phi_khong_hop_le !== ''
            ? (row as any).chi_phi_khong_hop_le
            : null,

        // Account fields
        tk_vt: row.tk_co && row.tk_co !== '' ? row.tk_co : null, // Map tk_co -> tk_vt
        tk_du: row.tk_no && row.tk_no !== '' ? row.tk_no : null, // Map tk_no -> tk_du
        ma_nx: row.ly_do_xuat && row.ly_do_xuat !== '' ? row.ly_do_xuat : null, // Map ly_do_xuat -> ma_nx

        // Numeric fields
        so_luong: Number(row.so_luong) || 0,
        gia_nt: Number(row.don_gia) || 0, // Map don_gia -> gia_nt
        tien_nt: Number(row.thanh_tien) || 0, // Map thanh_tien -> tien_nt
        gia: Number(row.don_gia_vnd) || 0, // Map don_gia_vnd -> gia
        tien: Number(row.thanh_tien_vnd) || 0, // Map thanh_tien_vnd -> tien

        // String fields with defaults
        ten_dvt: (row.vat_tu_data as any)?.dvt_data?.ten_dvt || '',
        ten_kho: row.kho_data?.ten_kho || '',
        ten_lo: '',
        ten_vi_tri: '',
        ma_lsx: row.ma_lenh_sx || '',

        // Integer fields with defaults
        lo_yn: 0,
        vi_tri_yn: 0,
        px_dd: 0,
        qc_yn: 0,
        sl_px: Number(row.so_luong) || 0,
        id_px: 0,
        line_px: 0,
        id_yc: 0,
        line_yc: 0,
        id_nhap: 0,
        line_nhap: 0,

        he_so: 1.0
      };

      return transformedRow;
    });

    const enhancedData = {
      ...data,
      chi_tiet: transformedDetailRows
    };

    onSubmit(enhancedData);
  };

  const handleClose = () => {
    setIsConfirm(true);
  };

  return (
    <div className='h-full flex-1 lg:overflow-hidden'>
      <AritoForm
        mode={formMode}
        initialData={initialData}
        title={formMode === 'add' ? 'Mới' : 'Sửa'}
        onSubmit={handleSubmit}
        onClose={handleClose}
        subTitle={'Phiếu xuất kho'}
        headerFields={<BasicInformationTabWrapper formMode={formMode} selectedObj={selectedObj} />}
        tabs={[
          {
            id: 'details',
            label: 'Chi tiết',
            component: (
              <DetailTab
                formMode={formMode}
                rows={detailRows}
                selectedRowUuid={selectedRowUuid}
                selectedCell={selectedCell}
                onRowClick={handleRowClick}
                onCellClick={handleCellClick}
                onAddRow={handleAddRow}
                onDeleteRow={handleDeleteRow}
                onCopyRow={handleCopyRow}
                onPasteRow={handlePasteRow}
                onMoveRow={handleMoveRow}
                onExport={handleExport}
                onPin={handlePin}
                onCellValueChange={handleCellValueChange}
              />
            )
          },
          {
            id: 'file',
            label: 'File',
            component: <FileTab formMode={formMode} />
          }
        ]}
        // from={
        //   <div className='flex w-full items-center justify-center gap-2 border-b border-gray-300 p-1 pr-2 lg:justify-end'>
        //     <Button
        //       className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold normal-case text-white'
        //       onClick={openExportDialog}
        //       title='Phiếu yêu cầu xuất kho'
        //     >
        //       Phiếu yêu cầu xuất kho
        //     </Button>
        //     <Button
        //       className='rounded-[2px] bg-teal-600 p-1 text-xs font-bold normal-case text-white'
        //       onClick={openProdOrderDialog}
        //       type='button'
        //       title='Lệnh sản xuất'
        //     >
        //       Lệnh sản xuất
        //     </Button>
        //   </div>
        // }
        bottomBar={<BottomBar totalQuantity={initialData?.tong_sl || 0} totalAmount={initialData?.tong_tien || 0} />}
      />

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </div>
  );
};

export default FormDialog;
