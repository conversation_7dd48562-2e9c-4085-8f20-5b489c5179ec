import AritoFormDateRangeDropdown from '@/components/custom/arito/form/form-date-range-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { khachHangSearchColumns } from '@/constants';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ngày c/từ(từ/đến)</Label>
          <AritoFormDateRangeDropdown fromDateName='fromDate' toDateName='toDate' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ(từ/đến)</Label>
          <FormField type='number' name='fromNumber' disabled={formMode === 'view'} className='mr-2 w-[215px]' />
          <FormField type='number' name='toNumber' disabled={formMode === 'view'} className='w-[210px]' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Loại xuất</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              name='export_type'
              options={[
                { value: '0', label: 'Tất cả' },
                { value: '3', label: '3. Cả hai' },
                { value: '1', label: '1. Xuất sổ sách' },
                { value: '2', label: '2. Xuất thực tế' }
              ]}
              defaultValue={'3411'}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Giao dịch</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              name='transaction'
              options={[
                { value: '0', label: 'Tất cả' },
                { value: 'NB', label: 'NB. Xuất nội bộ' },
                { value: 'DC', label: 'DC. Điều chỉnh' },
                { value: 'XK', label: 'XK. Xuất khác' },
                { value: 'X1', label: 'X1. Xuất nguyên vật liệu' },
                { value: 'TS', label: 'TS. Mua tài sản/công cụ' }
              ]}
              defaultValue={'3411'}
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mã đối tượng</Label>
          <div className='w-[250px]'>
            <SearchField
              type='text'
              disabled={formMode === 'view'}
              searchEndpoint='/'
              searchColumns={khachHangSearchColumns}
              dialogTitle='Danh mục đối tượng'
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải</Label>
          <div className='w-full'>
            <FormField type='text' name='description' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};
