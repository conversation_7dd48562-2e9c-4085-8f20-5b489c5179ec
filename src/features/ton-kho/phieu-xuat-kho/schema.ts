import { z } from 'zod';

const ChiTietXuatSchema = z.object({
  uuid: z.string().optional(),
  ma_vt: z.string().optional(),
  ten_vt: z.string().optional(),
  ma_dvt: z.string().optional(),
  ma_kho: z.string().optional(),
  ton_kho: z.coerce.number().optional(),
  so_luong: z.coerce.number().optional(),
  dich_danh: z.boolean().optional(),
  don_gia: z.coerce.number().optional(),
  thanh_tien: z.coerce.number().optional(),
  loai_ts_cc: z.string().optional(),
  tk_co: z.string().optional(),
  ly_do_xuat: z.string().optional(),
  tk_no: z.string().optional(),
  ma_bp: z.string().optional(),
  ma_vu_viec: z.string().optional(),
  dot_tt: z.string().optional(),
  ma_hd: z.string().optional(),
  phi: z.coerce.number().optional(),
  ma_sp: z.string().optional(),
  ma_lenh_sx: z.string().optional(),
  don_gia_vnd: z.coerce.number().optional(),
  thanh_tien_vnd: z.coerce.number().optional(),
  sl_da_xuat: z.coerce.number().optional(),
  so_px_thuc_te: z.string().optional(),
  dong_px: z.string().optional(),
  so_phieu_yc: z.string().optional(),
  dong_yc: z.string().optional(),
  vat_tu_data: z
    .object({
      ma_vt: z.string(),
      ten_vt: z.string()
    })
    .optional(),
  don_vi_tinh_data: z
    .object({
      ma_dvt: z.string(),
      ten_dvt: z.string()
    })
    .optional(),
  kho_data: z
    .object({
      ma_kho: z.string(),
      ten_kho: z.string()
    })
    .optional()
});

export const FormSchema = z.object({
  uuid: z.string().optional(),
  ma_gd: z.string().nonempty('Giao dịch không được để trống'),
  ma_nk: z.string().nonempty('Mã nhật ký không được để trống'), // UUID for document book
  so_ct: z.string().nonempty('Số chứng từ không được để trống'),
  ngay_ct: z.coerce.date(),
  ma_dt: z.string().nonempty('Mã đối tượng không được để trống'),
  ten_dt: z.string().nonempty('Tên đối tượng không được để trống'),
  dien_giai: z.string().optional(),
  ma_nt: z.string().nonempty('Loại tiền không được để trống'), // UUID for currency
  ty_gia: z.coerce.number().min(0, 'Tỷ giá không hợp lệ'),
  trang_thai: z.string().nonempty('Trạng thái không được để trống'),
  da_ghi_so: z.boolean().optional(),
  tong_tien: z.coerce.number().min(0, 'Tổng tiền không hợp lệ'),
  tong_sl: z.coerce.number().min(0, 'Tổng số lượng không hợp lệ'),
  ma_ct: z.string().nonempty('Mã chứng từ không được để trống'),
  chi_tiet_xuat: z.array(ChiTietXuatSchema),
  // Required fields with default values
  t_so_luong: z.coerce.number().default(0),
  t_tien_nt: z.coerce.number().default(0),
  t_tien: z.coerce.number().default(0),
  entity_model: z.string().default(''),
  nguoi_tao: z.string().default(''),
  ngay_tao: z.coerce.date().default(new Date())
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ma_gd: 'NB',
  ma_nk: '', // Will be set when document book is selected
  so_ct: '',
  ngay_ct: new Date(),
  ma_dt: '',
  ten_dt: '',
  dien_giai: '',
  ma_nt: '', // Will be set when currency is selected
  ty_gia: 1,
  trang_thai: 'Mới',
  da_ghi_so: false,
  tong_tien: 0,
  tong_sl: 0,
  ma_ct: '',
  chi_tiet_xuat: [],
  // Required fields with default values
  t_so_luong: 0,
  t_tien_nt: 0,
  t_tien: 0,
  entity_model: '',
  nguoi_tao: '',
  ngay_tao: new Date()
};

export const SearchFormSchema = z.object({
  tu_ngay: z.coerce.date(),
  den_ngay: z.coerce.date(),
  tu_so: z.coerce.number().min(0),
  den_so: z.coerce.number().min(0),
  loai_xuat: z.string().nonempty('Loại xuất không được để trống'),
  ma_gd: z.string().nonempty('Giao dịch không được để trống'),
  dien_giai: z.string().optional(),
  trang_thai: z.string().nonempty('Trạng thái không được để trống'),
  loc_theo_nguoi_sd: z.string().nonempty('Lọc theo người sd không được để trống')
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

export const initialSearchValues: SearchFormValues = {
  tu_ngay: new Date(),
  den_ngay: new Date(),
  tu_so: 0,
  den_so: 0,
  loai_xuat: '0',
  ma_gd: '0',
  dien_giai: '',
  trang_thai: 'all',
  loc_theo_nguoi_sd: '0'
};
