'use client';

import { useEffect } from 'react';
import Split from 'react-split';
import { useDialogState, useRowSelection, usePhieuXuatKhoIntegration } from './hooks/';
import { exportMainColumns, warehouseExportDetailColumns } from './cols-definition';
import { FormDialog, ActionBar, SearchDialog, ConfirmDialog } from './components';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito/';
import { PhieuXuatKho } from '@/types/schemas/phieu-xuat-kho.type';
import { AritoInputTable } from '@/components/custom/arito';
import { FormValues, initialFormValues } from './schema';
import { createFakeChiTietData } from './mock-data';
export function WarehouseExport() {
  // Custom hooks for state management
  const { selectedObj, selectedRowIndex, handleRowClick, clearSelection } = useRowSelection();
  const {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showSearchDialog,
    isCopyMode,
    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    closeSearchDialog,
    handleCopyButtonClick,
    handlePrintButtonClick,
    handleSearchButtonClick,
    handleExportButtonClick
  } = useDialogState(clearSelection);

  const {
    phieuXuatKhos,
    isLoading,
    addPhieuXuatKho,
    updatePhieuXuatKho,
    deletePhieuXuatKho,
    copyPhieuXuatKho,
    refreshPhieuXuatKhos
  } = usePhieuXuatKhoIntegration();

  // Load data when component mounts
  useEffect(() => {
    refreshPhieuXuatKhos();
  }, [refreshPhieuXuatKhos]);

  // Form submission handler
  const handleFormSubmit = (data: FormValues) => {
    // Map form data to API expected fields
    const enhancedData: any = {
      ...data,
      uuid: selectedObj?.uuid,
      entity_model: 'b0bb20d5-9da4-4fd8-8849-92b5faa6a4cc', // gán cứng giá trị cho entity_model vì lỗi API
      nguoi_tao: 'default_value', // gán cứng giá trị cho nguoi_tao
      ma_nk: 'eded2f34-486c-43f6-aa06-bf28acd2f4f7', // gán cứng giá trị cho ma_nk
      so_ct: 'fcc38898-9d2d-41c5-98ca-ddb07238b610',
      // ngay_tao: new Date(), // gán cứng giá trị cho ngay_tao
      ngay_ct: new Date(data.ngay_ct).toISOString().slice(0, 10),
      ngay_lct: new Date(data.ngay_ct).toISOString().slice(0, 10),
      unit_id: 1, // gán cứng giá trị cho unit_id
      t_so_luong: 0, // gán cứng giá trị cho t_so_luong
      t_tien_nt: 0, // gán cứng giá trị cho t_tien_nt
      t_tien: 0 // gán cứng giá trị cho t_tien
    };

    // Handle different form modes
    if (showAddDialog && !isCopyMode) {
      addPhieuXuatKho(enhancedData)
        .then(() => {
          closeAddDialog();
          clearSelection();
        })
        .catch((error: any) => {
          console.error('Error adding:', error);
        });
    } else if (showAddDialog && isCopyMode && selectedObj) {
      // Handle copy operation
      copyPhieuXuatKho(enhancedData)
        .then(() => {
          closeAddDialog();
          clearSelection();
        })
        .catch((error: any) => {
          console.error('Error copying:', error);
        });
    } else if (showEditDialog && selectedObj) {
      updatePhieuXuatKho(selectedObj.uuid, enhancedData)
        .then(() => {
          closeEditDialog();
          clearSelection();
        })
        .catch((error: any) => {
          console.error('Error updating:', error);
        });
    }
  };

  // Delete handler
  const handleDeleteConfirm = () => {
    if (selectedObj) {
      deletePhieuXuatKho(selectedObj.uuid)
        .then(() => {
          closeDeleteDialog();
          clearSelection();
        })
        .catch((error: any) => {
          console.error('Error deleting:', error);
        });
    }
  };

  // Transform data for display and add fake chi_tiet
  const transformedRows = phieuXuatKhos.map((item: PhieuXuatKho) => ({
    ...item,
    id: item.uuid,
    checkbox: false,
    // Add fake chi_tiet data for testing if not exists
    chi_tiet:
      (item as any)?.chi_tiet && (item as any)?.chi_tiet.length > 0 ? (item as any)?.chi_tiet : createFakeChiTietData()
  }));

  // Define tables for displ
  const tables = [
    {
      name: 'Tất cả',
      rows: transformedRows,
      columns: exportMainColumns
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {(showAddDialog || showEditDialog) && (
        <FormDialog
          formMode={showEditDialog ? 'edit' : 'add'}
          initialData={isCopyMode && selectedObj ? selectedObj : showEditDialog ? selectedObj : initialFormValues}
          onClose={showEditDialog ? closeEditDialog : closeAddDialog}
          selectedObj={showEditDialog ? selectedObj : null}
          onSubmit={handleFormSubmit}
        />
      )}

      {showDeleteDialog && (
        <ConfirmDialog
          open={showDeleteDialog}
          onClose={closeDeleteDialog}
          onConfirm={handleDeleteConfirm}
          selectedObj={selectedObj}
          clearSelection={clearSelection}
          title='Xoá dữ liệu'
          content='Bạn có chắc chắn muốn xóa không?'
        />
      )}

      {showSearchDialog && (
        <SearchDialog
          openSearchDialog={showSearchDialog}
          onCloseSearchDialog={closeSearchDialog}
          onSearch={handleSearchButtonClick}
        />
      )}

      {!showAddDialog && !showEditDialog && (
        <>
          <ActionBar
            onAddClick={openAddDialog}
            onEditClick={() => selectedObj && openEditDialog()}
            onDeleteClick={() => selectedObj && openDeleteDialog()}
            onCopyClick={() => selectedObj && handleCopyButtonClick()}
            onPrintClick={handlePrintButtonClick}
            onSearchClick={handleSearchButtonClick}
            onRefreshClick={refreshPhieuXuatKhos}
            onExportClick={handleExportButtonClick}
          />
          <div className='w-full overflow-hidden'>
            {isLoading && <LoadingOverlay />}
            {!isLoading && (
              <Split
                className='flex flex-1 flex-col overflow-hidden'
                direction='vertical'
                sizes={[50, 50]}
                minSize={200}
                gutterSize={8}
                gutterAlign='center'
                snapOffset={30}
                dragInterval={1}
                cursor='row-resize'
              >
                <div className='w-full overflow-hidden'>
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                  />
                </div>
                <div className='w-full flex-1 overflow-hidden'>
                  <AritoInputTable
                    value={(() => {
                      return selectedObj?.chi_tiet || [];
                    })()}
                    columns={warehouseExportDetailColumns}
                    tableActionButtons={['export', 'pin']}
                  />
                </div>
              </Split>
            )}
          </div>
        </>
      )}
    </div>
  );
}
