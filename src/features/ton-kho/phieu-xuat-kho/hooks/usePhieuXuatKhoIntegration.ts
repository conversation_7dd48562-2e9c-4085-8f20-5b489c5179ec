import { useCallback } from 'react';
import { PhieuXuatKho } from '@/types/schemas/phieu-xuat-kho.type';
import { usePhieuXuatKho } from '@/hooks/queries/usePhieuXuatKho';

export const usePhieuXuatKhoIntegration = () => {
  const { phieuXuatKhos, isLoading, addPhieuXuatKho, updatePhieuXuatKho, deletePhieuXuatKho, refreshPhieuXuatKhos } =
    usePhieuXuatKho();

  const copyPhieuXuatKho = useCallback(
    async (data: PhieuXuatKho) => {
      // Create a copy with a new document number
      const copyData = {
        ...data,
        dien_giai: data.dien_giai ? `${data.dien_giai} (Copy)` : 'Copy description'
      };
      return addPhieuXuatKho(copyData);
    },
    [addPhieuXuatKho]
  );

  return {
    phieuXuatKhos,
    isLoading,
    addPhieuXuat<PERSON>ho,
    updatePhieuXuatKho,
    deletePhieuXuatKho,
    copyPhieuXuatKho,
    refreshPhieuXuatKhos
  };
};
