import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const exportMainColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: () => <Checkbox onCheckedChange={() => {}} />,
    renderCell: (params: GridRenderCellParams) => <Checkbox checked={params.value} />
  },
  { field: 'trang_thai', headerName: 'Trạng thái', width: 120 },
  {
    field: 'so_ct',
    headerName: 'Số c/từ',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline'>{params.value}</div>
    )
  },
  { field: 'ngay_ct', headerName: 'Ngày c/từ', width: 120 },
  { field: 'ma_dt', headerName: 'Mã đối tượng', width: 120 },
  { field: 'ten_dt', headerName: 'Tên đối tượng', width: 200 },
  { field: 'dien_giai', headerName: 'Diễn giải', width: 200 },
  { field: 'tong_tien', headerName: 'Tổng tiền', type: 'number', width: 120 },
  { field: 'ma_nt', headerName: 'Ngoại tệ', width: 100 },
  { field: 'tong_sl', headerName: 'Tổng số lượng', type: 'number', width: 120 },
  { field: 'ma_ct', headerName: 'Mã ct', width: 120 }
];

export const warehouseExportDetailColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'ten_vt', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'ma_dvt', headerName: 'Đvt', width: 80 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'ton_kho', headerName: 'Tồn', width: 100 },
  { field: 'so_luong', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'dich_danh', headerName: 'Đích danh', type: 'boolean', width: 100 },
  { field: 'don_gia', headerName: 'Giá %s', type: 'number', width: 100 },
  { field: 'thanh_tien', headerName: 'Tiền %s', type: 'number', width: 100 },
  { field: 'loai_ts_cc', headerName: 'Loại TS/CC', width: 120 },
  { field: 'tk_co', headerName: 'Tk có', width: 100 },
  { field: 'ly_do_xuat', headerName: 'Lý do xuất', width: 120 },
  { field: 'tk_no', headerName: 'Tk nợ', width: 100 },
  { field: 'ma_bp', headerName: 'Bộ phận', width: 120 },
  { field: 'ma_vu_viec', headerName: 'Vụ việc', width: 120 },
  { field: 'dot_tt', headerName: 'Đợt thanh toán', width: 120 },
  { field: 'ma_hd', headerName: 'Khế ước', width: 120 },
  { field: 'phi', headerName: 'Phí', type: 'number', width: 100 },
  { field: 'ma_sp', headerName: 'Sản phẩm', width: 120 },
  { field: 'ma_lenh_sx', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'don_gia_vnd', headerName: 'Giá', type: 'number', width: 100 },
  { field: 'thanh_tien_vnd', headerName: 'Tiền', type: 'number', width: 100 },
  { field: 'sl_da_xuat', headerName: 'Sl đã xuất', type: 'number', width: 120 },
  { field: 'so_px_thuc_te', headerName: 'Số PX t/tế', width: 120 },
  { field: 'dong_px', headerName: 'Dòng px', width: 120 },
  { field: 'so_phieu_yc', headerName: 'Số phiếu YC', width: 120 },
  { field: 'dong_yc', headerName: 'Dòng YC', width: 120 }
];

export const warehouseExportItemColumns: GridColDef[] = [
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 200 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'stock', headerName: 'Tồn', width: 100 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'specific', headerName: 'Đích danh', type: 'boolean', width: 100 },
  { field: 'priceInVND', headerName: 'Giá VND', type: 'number', width: 100 },
  { field: 'amountInVND', headerName: 'Tiền VND', type: 'number', width: 100 },
  { field: 'creditAccount', headerName: 'Tk có', width: 100 },
  { field: 'exportReason', headerName: 'Lý do xuất', width: 120 },
  { field: 'debitAccount', headerName: 'Tk nợ', width: 100 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'case', headerName: 'Vụ việc', width: 120 },
  { field: 'paymentBatch', headerName: 'Đợt thanh toán', width: 120 },
  { field: 'contract', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', type: 'number', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'requestNumber', headerName: 'Số phiếu YC', width: 120 },
  { field: 'requestLine', headerName: 'Dòng YC', width: 120 }
];

export const exportSlipColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: () => <Checkbox onCheckedChange={() => {}} />
  },
  { field: 'productCode', headerName: 'Mã sản phẩm', width: 120 },
  { field: 'productName', headerName: 'Tên sản phẩm', width: 250 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'getQuantity', headerName: 'Sl lấy', type: 'number', width: 100 },
  { field: 'quantity', headerName: 'Số lượng', type: 'number', width: 100 },
  { field: 'remainingQuantity', headerName: 'Sl còn lại', type: 'number', width: 100 },
  { field: 'stock', headerName: 'Tồn', type: 'number', width: 100 },
  { field: 'warehouseCode', headerName: 'Mã kho', width: 100 },
  { field: 'lotNumber', headerName: 'Mã lô', width: 100 },
  { field: 'locationCode', headerName: 'Mã vị trí', width: 100 },
  { field: 'requestNumber', headerName: 'Số phiếu YC', width: 100 },
  { field: 'requestLine', headerName: 'Dòng YC', width: 100 },
  { field: 'department', headerName: 'Bộ phận', width: 120 },
  { field: 'case', headerName: 'Vụ việc', width: 120 },
  { field: 'contract', headerName: 'Hợp đồng', width: 120 },
  { field: 'paymentBatch', headerName: 'Đợt thanh toán', width: 120 },
  { field: 'contract', headerName: 'Khế ước', width: 120 },
  { field: 'fee', headerName: 'Phí', type: 'number', width: 100 },
  { field: 'product', headerName: 'Sản phẩm', width: 120 },
  { field: 'productionOrder', headerName: 'Lệnh sản xuất', width: 120 },
  { field: 'invalid', headerName: 'C/p không h/lệ', width: 120 }
];

export const releaseRequestSearchColumns: GridColDef[] = [
  {
    field: 'checkbox',
    headerName: '',
    width: 50,
    renderHeader: () => <Checkbox onCheckedChange={() => {}} />
  },
  { field: 'documentCode', headerName: 'Số c/từ', width: 120 },
  { field: 'documentDate', headerName: 'Ngày c/từ', width: 120 },
  { field: 'departmentCode', headerName: 'Mã bộ phận', width: 120 },
  { field: 'departmentName', headerName: 'Tên bộ phận', width: 250 },
  { field: 'totalQuantity', headerName: 'Tổng số lượng', type: 'number', width: 120 }
];

export const materialSearchColumns: GridColDef[] = [
  { field: 'materialCode', headerName: 'Mã vật tư', width: 120 },
  { field: 'materialName', headerName: 'Tên vật tư', width: 250 },
  { field: 'unitOfMeasure', headerName: 'Đvt', width: 80 },
  { field: 'group', headerName: 'Nhóm 1', width: 100 },
  { field: 'lotTracking', headerName: 'Theo dõi lô', width: 100, type: 'boolean' },
  { field: 'specification', headerName: 'Quy cách', width: 100, type: 'boolean' },
  { field: 'image', headerName: 'Hình ảnh', width: 100 }
];

export const warehouseSearchColumns: GridColDef[] = [
  { field: 'warehouseCode', headerName: 'Mã kho', width: 120 },
  { field: 'warehouseName', headerName: 'Tên kho', width: 250 },
  { field: 'unit', headerName: 'Đơn vị', width: 130 },
  { field: 'unitTracking', headerName: 'Theo dõi đơn vị', width: 100, type: 'boolean' }
];

export const lotSearchColumns: GridColDef[] = [
  { field: 'lotNumber', headerName: 'Mã lô', width: 120 },
  { field: 'lotName', headerName: 'Tên lô', width: 250 }
];

export const locationSearchColumns: GridColDef[] = [
  { field: 'locationCode', headerName: 'Mã vị trí', width: 120 },
  { field: 'locationName', headerName: 'Tên vị trí', width: 250 }
];

export const companySearchColumns: GridColDef[] = [
  { field: 'checkbox', headerName: '', width: 50, renderHeader: () => <Checkbox onCheckedChange={() => {}} /> },
  { field: 'companyCode', headerName: 'Mã đơn vị', width: 120 },
  { field: 'companyName', headerName: 'Tên đơn vị', width: 250 },
  { field: 'id', headerName: 'ID', width: 100 }
];
