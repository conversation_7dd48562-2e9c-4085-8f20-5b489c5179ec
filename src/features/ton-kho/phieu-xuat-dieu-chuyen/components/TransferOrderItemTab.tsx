import { transferOrderItemColumns } from '../cols-definition';
import { AritoInputTable } from '@/components/custom/arito';

interface TransferOrderItemTabProps {
  value?: any[];
  onChange?: (newValue: any[]) => void;
  formMode: 'edit' | 'view' | 'add';
}

export const TransferOrderItemTab = ({ value, onChange, formMode }: TransferOrderItemTabProps) => {
  return (
    <div className='h-[270px] w-screen'>
      <AritoInputTable value={value} columns={transferOrderItemColumns} onChange={onChange} mode={formMode} />
    </div>
  );
};
