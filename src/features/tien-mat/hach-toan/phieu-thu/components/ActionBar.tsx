import { Plus, Edit, Trash, Copy, Printer, Search } from 'lucide-react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoMenuButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick?: () => void;
  onCopyClick?: () => void;
  onPrintClick?: () => void;
  onSearchClick?: () => void;
  onRefreshClick?: () => void;
  onFixedColumnsClick?: () => void;
  onMultiPrintClick?: () => void;
  onExportDataClick?: () => void;
  onDownloadExcelTemplateClick?: () => void;
  onImportFromExcelClick?: () => void;
  isEditDisabled?: boolean;
  isDeleteDisabled?: boolean;
  isCopyDisabled?: boolean;
  className?: string;
}

export const ActionBar: React.FC<ActionBarProps> = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onPrintClick,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onMultiPrintClick,
  onExportDataClick,
  onDownloadExcelTemplateClick,
  onImportFromExcelClick,
  isEditDisabled = true,
  isDeleteDisabled = true,
  isCopyDisabled = true,
  className
}) => {
  return (
    <AritoActionBar className={className} titleComponent={<h1 className='text-xl font-bold'>Phiếu thu tiền</h1>}>
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='secondary' />
      <AritoActionButton title='Sửa' icon={Edit} onClick={onEditClick} variant='secondary' disabled={isEditDisabled} />
      {onDeleteClick && (
        <AritoActionButton
          title='Xóa'
          icon={Trash}
          onClick={onDeleteClick}
          variant='secondary'
          disabled={isDeleteDisabled}
        />
      )}
      {onCopyClick && (
        <AritoActionButton
          title='Sao chép'
          icon={Copy}
          onClick={onCopyClick}
          variant='secondary'
          disabled={isCopyDisabled}
        />
      )}
      {onPrintClick && <AritoActionButton title='In ấn' icon={Printer} onClick={onPrintClick} variant='secondary' />}
      {onSearchClick && (
        <AritoActionButton title='Tìm kiếm' icon={Search} onClick={onSearchClick} variant='secondary' />
      )}
      <AritoMenuButton
        items={[
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: onFixedColumnsClick,
            group: 0
          },
          {
            title: 'In nhiều',
            icon: <AritoIcon icon={883} />,
            onClick: onMultiPrintClick,
            group: 1
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: onExportDataClick,
            group: 1
          },
          {
            title: 'Tải mẫu Excel',
            icon: <AritoIcon icon={28} />,
            onClick: onDownloadExcelTemplateClick,
            group: 2
          },
          {
            title: 'Lấy dữ liệu từ Excel',
            icon: <AritoIcon icon={29} />,
            onClick: onImportFromExcelClick,
            group: 2
          }
        ]}
      />
    </AritoActionBar>
  );
};
