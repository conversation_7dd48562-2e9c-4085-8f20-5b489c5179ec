'use client';

import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import {
  DelegateExpenseTab,
  ExchangeRateTab,
  PaymentInfoTab,
  DetailItemTab,
  BasicInfoTab,
  BankFeeTab,
  ActionBar,
  BottomBar,
  OtherTab,
  TaxTab,
  From
} from '@/features/tien-gui/hach-toan/giay-bao-no/components';
import {
  exportDebitAdviceDetailColumns,
  getChangingValueDebitAdviceColumns
} from '@/features/tien-gui/hach-toan/giay-bao-no/cols-definition';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import { UpdatePaymentProposal, SearchForm } from './components/popup';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoForm } from '@/components/custom/arito/form';
import { filterableCols } from './types/filterCols';
import { calculateTotals } from './utils/Caculate';
import { filterValues } from './types/filterTabs';

export default function GiayBaoNoPage() {
  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [rows, setRows] = useState<any[]>([]);
  const [showUpdatePaymentProposalForm, setShowUpdatePaymentProposalForm] = useState<boolean>(false);
  const [showSearchForm, setShowSearchForm] = useState<boolean>(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    setSelectedObj(obj);
    setInputDetails(obj.details || []);
  };

  const handleFormSubmit = async (data: any) => {};

  const onSearchClick = () => {
    setShowSearchForm(true);
  };
  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getChangingValueDebitAdviceColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getChangingValueDebitAdviceColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getChangingValueDebitAdviceColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      {showForm ? (
        <div className='flex h-full flex-col'>
          <div className='flex-1 overflow-y-auto pb-[120px]'>
            <AritoForm
              mode={formMode}
              initialData={currentObj || undefined}
              onSubmit={handleFormSubmit}
              onClose={handleCloseForm}
              subTitle='Giấy báo nợ'
              headerFields={<BasicInfoTab formMode={formMode} />}
              from={<From setShowPopUpForm={setShowUpdatePaymentProposalForm} />}
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailItemTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'tax',
                  label: 'Thuế',
                  component: <TaxTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'paymentInfo',
                  label: 'Thông tin thanh toán',
                  component: <PaymentInfoTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'exchangeRate',
                  label: 'Tỷ giá',
                  component: <ExchangeRateTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'bankFee',
                  label: 'Phí ngân hàng',
                  component: <BankFeeTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'delegateExpense',
                  label: 'Uỷ nhiệm chi',
                  component: <DelegateExpenseTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
                }
              ]}
            />
          </div>
          <div className='fixed bottom-0 left-0 right-0 bg-white'>
            <BottomBar totalMoney={calculateTotals(inputDetails)} totalPayment={0} formMode={formMode} />
          </div>
        </div>
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => {}}
            onCopyClick={() => {}}
            onSearchClick={onSearchClick}
            onRefreshClick={() => {}}
            onPrintClick={() => {}}
          />
          <Split
            className='flex flex-1 flex-col overflow-hidden'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
                filterableColumns={filterableCols}
              />
            </div>
            <div className='w-full overflow-hidden'>
              <AritoInputTable
                value={selectedObj?.details || []}
                columns={exportDebitAdviceDetailColumns}
                mode='view'
              />
            </div>
          </Split>
        </>
      )}
      {showUpdatePaymentProposalForm && (
        <UpdatePaymentProposal
          open={showUpdatePaymentProposalForm}
          onClose={setShowUpdatePaymentProposalForm}
          initialData={[]}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}
      {showSearchForm && (
        <SearchForm
          open={showSearchForm}
          onClose={setShowSearchForm}
          initialData={[]}
          handleSubmit={() => {}}
          formMode='add'
        />
      )}
    </div>
  );
}
