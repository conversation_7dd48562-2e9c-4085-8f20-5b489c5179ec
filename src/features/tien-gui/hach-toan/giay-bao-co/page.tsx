'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import Split from 'react-split';
import { filter } from 'lodash';
import {
  ExchangeRateTab,
  PaymentInfoTab,
  DetailItemTab,
  BasicInfoTab,
  BankFeeTab,
  ActionBar,
  BottomBar,
  OtherTab
} from '@/features/tien-gui/hach-toan/giay-bao-co/components';
import {
  exportCreditAdviceDetailColumns,
  getChangingValueCreditAdviceColumns
} from '@/features/tien-gui/hach-toan/giay-bao-co/cols-definition';
import { dropdownTime, dropdownStatus, dropdownDoc } from '@/features/tien-gui/hach-toan/giay-bao-co/dropdownField';
import { exportCreditAdviceSchema } from '@/features/tien-gui/hach-toan/giay-bao-co/schemas';
import { AritoColoredDot } from '@/components/custom/arito/icon/colored-dot';
import { AritoInputTable } from '@/components/custom/arito/input-table';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { AritoForm } from '@/components/custom/arito/form';
import { calculateTotals } from './utils/Caculate';
import { filterValues } from './types/filterTabs';

export default function GiayBaoCoPage() {
  // Configure filterable columns
  const filterableColumns = [
    {
      field: 'status',
      headerName: 'Trạng thái',
      type: 'checkbox' as const,
      options: dropdownStatus
    },
    {
      field: 'date',
      headerName: 'Ngày c/từ',
      type: 'dateRange' as const,
      dateOptions: dropdownTime
    },
    {
      field: 'doc_type',
      headerName: 'Loại chứng từ',
      type: 'checkbox' as const,
      options: dropdownDoc
    }
  ];

  const [showForm, setShowForm] = useState<boolean>(false);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('view');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [inputDetails, setInputDetails] = useState<any[]>([]);
  const [rows, setRows] = useState<any[]>([]);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setInputDetails(obj.details || []);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setInputDetails([]);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const obj = params.row as any;
    setSelectedObj(obj);
    setInputDetails(obj.details || []);
  };

  const handleFormSubmit = async (data: any) => {};

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, handleOpenEditForm)
    },
    ...filterValues.map(filterValue => {
      const filteredRows = filter(rows, row => row.status === filterValue.value);
      return {
        name: filterValue.name,
        rows: filteredRows,
        columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, handleOpenEditForm),
        icon: <AritoColoredDot color={filterValue.color} className='mr-2' />,
        tabProps: { className: 'whitespace-nowrap' }
      };
    }),
    ...(filterValues.length > 0
      ? [
          {
            name: 'Khác',
            rows: rows.filter(row => !filterValues.some(filterValue => row.status === filterValue.value)),
            columns: getChangingValueCreditAdviceColumns(handleOpenViewForm, handleOpenEditForm),
            icon: <AritoColoredDot color='black' className='mr-2' />,
            tabProps: { className: 'whitespace-nowrap' }
          }
        ]
      : [])
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col'>
      {showForm ? (
        <div className='h-full flex-1 overflow-auto'>
          <AritoForm
            mode={formMode}
            initialData={currentObj || undefined}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
            schema={exportCreditAdviceSchema}
            subTitle='Giấy báo có'
            headerFields={<BasicInfoTab formMode={formMode} />}
            tabs={[
              {
                id: 'details',
                label: 'Chi tiết',
                component: <DetailItemTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              },
              {
                id: 'paymentInfo',
                label: 'Thông tin thanh toán',
                component: <PaymentInfoTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              },
              {
                id: 'exchangeRate',
                label: 'Tỷ giá',
                component: <ExchangeRateTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              },
              {
                id: 'bankFee',
                label: 'Phí ngân hàng',
                component: <BankFeeTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              },
              {
                id: 'other',
                label: 'Khác',
                component: <OtherTab value={inputDetails} onChange={setInputDetails} formMode={formMode} />
              }
            ]}
          />
          <div className='fixed bottom-0 left-0 right-0 bg-white'>
            <BottomBar totalMoney={calculateTotals(inputDetails)} totalPayment={0} formMode={formMode} />
          </div>
        </div>
      ) : (
        <>
          <ActionBar
            onAddClick={handleOpenAddForm}
            onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
            onDeleteClick={() => {}}
            onCopyClick={() => {}}
            onSearchClick={() => {}}
            onRefreshClick={() => {}}
            onPrintClick={() => {}}
          />
          <Split
            className='flex flex-1 flex-col'
            direction='vertical'
            sizes={[50, 50]}
            minSize={200}
            gutterSize={8}
            gutterAlign='center'
            snapOffset={30}
            dragInterval={1}
            cursor='row-resize'
          >
            <div className='w-full overflow-auto'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                selectedRowId={selectedObj?.id || undefined}
                filterableColumns={filterableColumns}
              />
            </div>
            <div className='overflow-x-auto'>
              <AritoInputTable
                value={selectedObj?.details || []}
                columns={exportCreditAdviceDetailColumns}
                mode='view'
                className='w-full'
                tableActionButtons={['moveUp', 'moveDown', 'export', 'pin', 'copy', 'paste']}
              />
            </div>
          </Split>
        </>
      )}
    </div>
  );
}
