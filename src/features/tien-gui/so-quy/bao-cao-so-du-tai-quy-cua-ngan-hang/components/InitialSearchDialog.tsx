import React from 'react';
import { AritoHeaderTabs, AritoForm, AritoDialog, AritoIcon, BottomBar } from '@/components/custom/arito';
import { initialSearchValues, SearchFormValues } from '../schema';
import { BasicInfoTab, DetailTab, OtherTab } from './tabs';
import { useSearchDialogState } from '../hooks';
import { FormMode } from '@/types/form';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: SearchFormValues) => void;
}

export default function InitialSearchDialog({ open, onClose, onSearch }: InitialSearchDialogProps) {
  const { state, actions } = useSearchDialogState();

  const handleSubmit = (data: SearchFormValues) => {
    // Merge form data with search dialog state following bang_ke_chung_tu pattern
    const mergedData: SearchFormValues = {
      ...data,
      tk: state.taiKhoan?.uuid || data.tk || ''
    };
    onSearch(mergedData);
  };

  const formMode: FormMode = 'add';

  const tabs = [
    {
      id: 'chi-tiet',
      label: 'Chi tiết',
      component: <DetailTab formMode={formMode} searchState={{ state, actions }} />
    },
    {
      id: 'khac',
      label: 'Khác',
      component: <OtherTab formMode={formMode} />
    }
  ];

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo số dư tại quỹ của ngân hàng'
      titleIcon={<AritoIcon icon={699} />}
      maxWidth='lg'
    >
      <div className='flex size-full flex-col overflow-hidden'>
        <div className='max-h-[calc(100vh-120px)] flex-1 overflow-auto'>
          <AritoForm<SearchFormValues>
            initialData={initialSearchValues}
            onSubmit={handleSubmit}
            className='!static !w-full'
            hasAritoActionBar={false}
            headerFields={
              <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
                <BasicInfoTab formMode={formMode} searchState={{ state, actions }} />

                <AritoHeaderTabs tabs={tabs} />
              </div>
            }
            classNameBottomBar='relative w-full flex justify-end gap-2'
            bottomBar={<BottomBar mode={formMode} onClose={onClose} />}
          />
        </div>
      </div>
    </AritoDialog>
  );
}
