import { RefreshCw, Table, FileDown, Printer, Search } from 'lucide-react';
import { AritoActionButton, AritoMenuButton, AritoActionBar, AritoIcon } from '@/components/custom/arito';
import { SearchFormValues } from '../schema';

interface Props {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onFixedColumnsClick: () => void;
  onExportDataClick: () => void;
  onEditPrintTemplateClick: () => void;
  searchParams: SearchFormValues;
  className?: string;
}

export function ActionBar({
  className,
  onSearchClick,
  onRefreshClick,
  onFixedColumnsClick,
  onExportDataClick,
  onEditPrintTemplateClick,
  searchParams
}: Props) {
  const getSubTitle = () => {
    if (searchParams?.ngay_ct2) {
      const formattedDate = searchParams.ngay_ct2.toLocaleDateString('vi-VN');
      return `<PERSON>áo cáo tại ngày ${formattedDate}`;
    }
    if (searchParams?.tk) {
      return `Tài khoản: ${searchParams.tk}`;
    }
    return '';
  };

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='relative text-xl font-bold'>Báo cáo số dư tại quỹ của ngân hàng</h1>
          {getSubTitle() && <p className='text-sm text-gray-600'>{getSubTitle()}</p>}
        </div>
      }
      className={className}
    >
      <>
        <AritoActionButton
          title='Tìm kiếm'
          icon={Search}
          onClick={onSearchClick}
          variant='primary'
          shortcut='Alt + S'
        />
        <AritoActionButton title='Làm mới' icon={RefreshCw} onClick={onRefreshClick} />
        <AritoActionButton title='Cố định cột' icon={Table} onClick={onFixedColumnsClick} />
        <AritoActionButton
          title='Kết xuất dữ liệu'
          icon={FileDown}
          onClick={onExportDataClick}
          shortcut='Alt + D, Ctrl + D'
        />
        <AritoMenuButton
          items={[
            {
              title: 'Chỉnh sửa mẫu in',
              icon: <AritoIcon icon={555} />,
              onClick: onEditPrintTemplateClick,
              group: 0
            }
          ]}
        />
      </>
    </AritoActionBar>
  );
}
