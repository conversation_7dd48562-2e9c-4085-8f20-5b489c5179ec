import { GridColDef } from '@mui/x-data-grid';
g
// Bank balance report columns following bang_ke_chung_tu pattern
export const getBankBalanceColumns = (): GridColDef[] => [
  {
    field: 'stt',
    headerName: 'STT',
    width: 80,
    type: 'number'
  },
  {
    field: 'tk',
    headerName: 'Tài khoản',
    width: 120
  },
  {
    field: 'ten_tk',
    headerName: 'Tên tài khoản',
    width: 250
  },
  {
    field: 'du_no',
    headerName: 'Dư nợ',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => {
      return params.value ? new Intl.NumberFormat('vi-VN').format(params.value) : '0';
    }
  },
  {
    field: 'du_no_nt',
    headerName: 'Dư nợ NT',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => {
      return params.value ? new Intl.NumberFormat('vi-VN').format(params.value) : '0';
    }
  },
  {
    field: 'du_co',
    headerName: 'D<PERSON> có',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => {
      return params.value ? new Intl.NumberFormat('vi-VN').format(params.value) : '0';
    }
  },
  {
    field: 'du_co_nt',
    headerName: 'Dư có NT',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => {
      return params.value ? new Intl.NumberFormat('vi-VN').format(params.value) : '0';
    }
  },
  {
    field: 'du',
    headerName: 'Dư',
    width: 150,
    type: 'number',
    valueFormatter: (params: any) => {
      return params.value ? new Intl.NumberFormat('vi-VN').format(params.value) : '0';
    }
  }
];

// Keep legacy export for backward compatibility
export const exportAccountColumns = getBankBalanceColumns;

export const exportPrintColColums: GridColDef[] = [
  { field: 'col', headerName: 'Cột', width: 150 },
  { field: 'colName', headerName: 'Tên cột', width: 150 },
  { field: 'colNameEng', headerName: 'Tên tiếng anh', width: 150 },
  { field: 'colWidth', headerName: 'Độ rộng', width: 150 },
  { field: 'colType', headerName: 'Định dạng', width: 150 },
  { field: 'colAlign', headerName: 'Căn chỉnh', width: 150 },
  { field: 'colBold', headerName: 'In đậm', width: 150 },
  { field: 'colItalic', headerName: 'In nghiêng', width: 150 },
  { field: 'colSum', headerName: 'Tổng', width: 150 }
];
