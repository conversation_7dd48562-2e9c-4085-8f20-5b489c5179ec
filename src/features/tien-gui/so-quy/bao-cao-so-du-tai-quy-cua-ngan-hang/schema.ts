// Search form interface following bang_ke_chung_tu pattern
export interface SearchFormValues {
  tk?: string;
  ngay_ct2: Date;
  ma_unit?: string;
  mau_bc?: number;
  data_analysis_struct?: string;
}

// Initial search values following bang_ke_chung_tu pattern
export const initialSearchValues: SearchFormValues = {
  tk: '',
  ngay_ct2: new Date(),
  ma_unit: '',
  mau_bc: 20,
  data_analysis_struct: ''
};

// Table row data interface - matching backend API from memories
export interface TableRowData {
  id?: string | number; // For MUI DataGrid
  syspivot?: string;
  systotal?: string;
  sysprint?: string;
  sysorder?: number;
  stt?: number;
  tk: string;
  ct_yn?: string;
  du_no?: number;
  du_no_nt?: number;
  du_co?: number;
  du_co_nt?: number;
  ten_tk: string;
  du?: number;
  isTotal?: boolean;
}
