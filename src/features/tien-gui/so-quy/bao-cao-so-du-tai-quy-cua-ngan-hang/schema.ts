import { z } from 'zod';

// Search form schema with validation following bang_ke_chung_tu pattern
export const SearchFormSchema = z.object({
  // Backend API fields based on memories
  tk: z.string().optional(), // Account code (string with FIND extend)
  ngay_ct2: z.coerce.date(), // Date (format YYYYMMDD)
  ma_unit: z.string().optional(), // Unit code (string)
  mau_bc: z.number().optional(), // Report template (integer)
  data_analysis_struct: z.string().optional() // Analysis structure (string)
});

export type SearchFormValues = z.infer<typeof SearchFormSchema>;

// Initial search values following bang_ke_chung_tu pattern
export const initialSearchValues: SearchFormValues = {
  tk: '',
  ngay_ct2: new Date(),
  ma_unit: '',
  mau_bc: 20,
  data_analysis_struct: ''
};

// Table row data interface - matching backend API from memories
export interface TableRowData {
  id?: string | number; // For MUI DataGrid
  syspivot?: string;
  systotal?: string;
  sysprint?: string;
  sysorder?: number;
  stt?: number;
  tk: string;
  ct_yn?: string;
  du_no?: number;
  du_no_nt?: number;
  du_co?: number;
  du_co_nt?: number;
  ten_tk: string;
  du?: number;
  isTotal?: boolean;
}
