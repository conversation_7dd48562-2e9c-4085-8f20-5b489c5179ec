import { useState, useEffect, useCallback } from 'react';
import { GridRowParams } from '@mui/x-data-grid';
import { getBankBalanceColumns } from '../cols-definition';
import { SearchFormValues, TableRowData } from '../schema';
import { mockBankBalanceData } from '../mock-data';

export const useTableData = (searchParams: SearchFormValues) => {
  const [data, setData] = useState<TableRowData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [selectedRow, setSelectedRow] = useState<TableRowData | null>(null);

  const fetchData = useCallback(() => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate loading delay with setTimeout (non-promise approach)
      setTimeout(() => {
        try {
          // Always show all mock data regardless of filter
          // In real implementation, searchParams will be sent to API
          setData(mockBankBalanceData);
          setIsLoading(false);
        } catch (err) {
          setError(new Error('Có lỗi xảy ra khi tải dữ liệu'));
          console.error('Error fetching data:', err);
          setIsLoading(false);
        }
      }, 1000);
    } catch (err) {
      setError(new Error('Có lỗi xảy ra khi tải dữ liệu'));
      console.error('Error fetching data:', err);
      setIsLoading(false);
    }
  }, [searchParams]);

  const refreshData = () => {
    fetchData();
  };

  const handleRowClick = (params: GridRowParams) => {
    setSelectedRow(params.row as TableRowData);
  };

  // Load data when searchParams change or on mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const tables = [
    {
      name: 'Báo cáo số dư tại quỹ của ngân hàng',
      rows: data,
      columns: getBankBalanceColumns(),
      tabProps: { className: 'whitespace-nowrap' }
    }
  ];

  return {
    tables,
    data,
    isLoading,
    error,
    selectedRow,
    handleRowClick,
    refreshData
  };
};
