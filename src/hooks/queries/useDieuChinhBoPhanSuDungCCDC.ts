import { useState, useEffect, useCallback } from 'react';
import {
  DieuChinhBoPhanSuDungCCDC,
  DieuChinhBoPhanSuDungCCDCResponse,
  DieuChinhBoPhanSuDungCCDCInput
} from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseDieuChinhBoPhanSuDungCCDCReturn {
  dieuChinhBoPhanSuDungCCDCs: DieuChinhBoPhanSuDungCCDC[];
  isLoading: boolean;
  addDieuChinhBoPhanSuDungCCDC: (newItem: DieuChinhBoPhanSuDungCCDCInput) => Promise<void>;
  updateDieuChinhBoPhanSuDungCCDC: (uuid: string, updatedItem: any) => Promise<void>;
  deleteDieuChinhBoPhanSuDungCCDC: (uuid: string) => Promise<void>;
  refreshDieuChinhBoPhanSuDungCCDC: () => Promise<void>;
}

export const useDieuChinhBoPhanSuDungCCDC = (
  initialData: DieuChinhBoPhanSuDungCCDC[] = []
): UseDieuChinhBoPhanSuDungCCDCReturn => {
  const [dieuChinhBoPhanSuDungCCDCs, setDieuChinhBoPhanSuDungCCDC] = useState<DieuChinhBoPhanSuDungCCDC[]>(initialData);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { entity } = useAuth();

  const fetchDieuChinhBoPhanSuDungCCDC = useCallback(async (): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    const response = await api.get<DieuChinhBoPhanSuDungCCDCResponse>(
      `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_BO_PHAN_SU_DUNG_CCDC}/`
    );

    const mappedData: DieuChinhBoPhanSuDungCCDC[] = response.data.results;

    setDieuChinhBoPhanSuDungCCDC(mappedData);
    setIsLoading(false);
  }, [entity?.slug]);

  const addDieuChinhBoPhanSuDungCCDC = async (newItem: DieuChinhBoPhanSuDungCCDCInput): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      if (newItem.ma_cc === null) {
        throw new Error('Mã công cụ không được để trống');
      }

      if (newItem.ma_bp === null) {
        throw new Error('Mã bộ phận không được để trống');
      }

      const response = await api.post(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_BO_PHAN_SU_DUNG_CCDC}/`,
        newItem
      );
      setDieuChinhBoPhanSuDungCCDC(prev => [...prev, response.data]);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra';
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const updateDieuChinhBoPhanSuDungCCDC = async (uuid: string, updatedItem: DieuChinhBoPhanSuDungCCDCInput) => {
    if (!entity?.slug) return;
    try {
      setIsLoading(true);
      const response = await api.put(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_BO_PHAN_SU_DUNG_CCDC}/${uuid}/`,
        updatedItem
      );

      // Update the item in the list
      const updatedItemData: DieuChinhBoPhanSuDungCCDC = response.data;

      setDieuChinhBoPhanSuDungCCDC(prev =>
        prev.map(item => (item.uuid === updatedItemData.uuid ? updatedItemData : item))
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Lỗi khi cập nhật điều chỉnh bộ phận sử dụng CCDC';
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDieuChinhBoPhanSuDungCCDC = async (uuid: string): Promise<void> => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.DIEU_CHINH_BO_PHAN_SU_DUNG_CCDC}/${uuid}/`);
      setDieuChinhBoPhanSuDungCCDC(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Lỗi khi xóa điều chỉnh bộ phận sử dụng CCDC';
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDieuChinhBoPhanSuDungCCDC();
  }, [fetchDieuChinhBoPhanSuDungCCDC]);

  return {
    dieuChinhBoPhanSuDungCCDCs,
    isLoading,
    addDieuChinhBoPhanSuDungCCDC,
    updateDieuChinhBoPhanSuDungCCDC,
    deleteDieuChinhBoPhanSuDungCCDC,
    refreshDieuChinhBoPhanSuDungCCDC: fetchDieuChinhBoPhanSuDungCCDC
  };
};
