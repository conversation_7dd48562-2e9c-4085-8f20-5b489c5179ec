import { useState, useEffect, useCallback } from 'react';
import { NgoaiTe, NgoaiTeInput, NgoaiTeResponse } from '@/types/schemas/ngoai-te.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface CurrencyFormattedData {
  ma_nt: string;
  ten_nt: string;
  ten_nt2?: string | null;
  tk_pscl_no?: string | null;
  tk_pscl_co?: string | null;
  stt: number;
  ra_ndec: number;
  cach_doc?: string | null;
  ra_1?: string | null;
  ra_2?: string | null;
  ra_3?: string | null;
  ra_4?: string | null;
  ra_5?: string | null;
  ra_12?: string | null;
  ra_22?: string | null;
  ra_32?: string | null;
  ra_42?: string | null;
  ra_52?: string | null;
  status: string | number;
}

interface UseNgoaiTeReturn {
  currencies: NgoaiTe[];
  isLoading: boolean;
  addCurrency: (newCurrency: CurrencyFormattedData) => Promise<void>;
  updateCurrency: (uuid: string, updatedCurrency: CurrencyFormattedData) => Promise<void>;
  deleteCurrency: (uuid: string) => Promise<void>;
  refreshCurrencies: () => Promise<void>;
}

export const useNgoaiTe = (initialCurrencies: NgoaiTe[] = []): UseNgoaiTeReturn => {
  const [currencies, setCurrencies] = useState<NgoaiTe[]>(initialCurrencies);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const { entity } = useAuth();

  const fetchCurrencies = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<NgoaiTeResponse>(`/entities/${entity.slug}/erp/${QUERY_KEYS.NGOAI_TE}/`);
      setCurrencies(response.data.results);
    } catch (error) {
      console.error('Error fetching currencies:', error);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug, setIsLoading, setCurrencies]);

  const addCurrency = async (newCurrency: CurrencyFormattedData) => {
    if (!entity?.slug) return;

    try {
      const currencyInput: NgoaiTeInput = {
        ma_nt: newCurrency.ma_nt,
        ten_nt: newCurrency.ten_nt,
        ten_nt2: newCurrency.ten_nt2,
        tk_pscl_no: newCurrency.tk_pscl_no,
        tk_pscl_co: newCurrency.tk_pscl_co,
        stt: typeof newCurrency.stt === 'string' ? parseInt(newCurrency.stt as string) : newCurrency.stt,
        ra_ndec:
          typeof newCurrency.ra_ndec === 'string' ? parseInt(newCurrency.ra_ndec as string) : newCurrency.ra_ndec,
        ra_1: newCurrency.ra_1 || null,
        ra_2: newCurrency.ra_2 || null,
        ra_3: newCurrency.ra_3 || null,
        ra_4: newCurrency.ra_4 || null,
        ra_5: newCurrency.ra_5 || null,
        ra_12: newCurrency.ra_12 || null,
        ra_22: newCurrency.ra_22 || null,
        ra_32: newCurrency.ra_32 || null,
        ra_42: newCurrency.ra_42 || null,
        ra_52: newCurrency.ra_52 || null,
        cach_doc: newCurrency.ten_nt,
        status: String(newCurrency.status),
        entity_model: entity?.uuid
      };
      console.log('Currency input:', currencyInput);
      await api.post(`/entities/${entity.slug}/erp/${QUERY_KEYS.NGOAI_TE}/`, currencyInput);
      await fetchCurrencies();
    } catch (error) {
      console.error('Error adding currency:', error);
      throw error;
    }
  };

  const updateCurrency = async (uuid: string, updatedCurrency: CurrencyFormattedData) => {
    if (!entity?.slug) return;

    try {
      const currencyInput: NgoaiTeInput = {
        ma_nt: updatedCurrency.ma_nt,
        ten_nt: updatedCurrency.ten_nt,
        ten_nt2: updatedCurrency.ten_nt2,
        tk_pscl_no: updatedCurrency.tk_pscl_no,
        tk_pscl_co: updatedCurrency.tk_pscl_co,
        stt: typeof updatedCurrency.stt === 'string' ? parseInt(updatedCurrency.stt as string) : updatedCurrency.stt,
        ra_ndec:
          typeof updatedCurrency.ra_ndec === 'string'
            ? parseInt(updatedCurrency.ra_ndec as string)
            : updatedCurrency.ra_ndec,
        ra_1: updatedCurrency.ra_1 || null,
        ra_2: updatedCurrency.ra_2 || null,
        ra_3: updatedCurrency.ra_3 || null,
        ra_4: updatedCurrency.ra_4 || null,
        ra_5: updatedCurrency.ra_5 || null,
        ra_12: updatedCurrency.ra_12 || null,
        ra_22: updatedCurrency.ra_22 || null,
        ra_32: updatedCurrency.ra_32 || null,
        ra_42: updatedCurrency.ra_42 || null,
        ra_52: updatedCurrency.ra_52 || null,
        cach_doc: updatedCurrency.ten_nt,
        status: String(updatedCurrency.status),
        entity_model: entity?.uuid
      };

      await api.patch(`/entities/${entity.slug}/erp/${QUERY_KEYS.NGOAI_TE}/${uuid}/`, currencyInput);
      await fetchCurrencies();
    } catch (error) {
      console.error('Error updating currency:', error);
      throw error;
    }
  };

  const deleteCurrency = async (uuid: string) => {
    if (!entity?.slug) return;

    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.NGOAI_TE}/${uuid}/`);
      await fetchCurrencies();
    } catch (error) {
      console.error('Error deleting currency:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (entity?.slug) {
      fetchCurrencies();
    }
  }, [entity?.slug, fetchCurrencies]);

  return {
    currencies,
    isLoading,
    addCurrency,
    updateCurrency,
    deleteCurrency,
    refreshCurrencies: fetchCurrencies
  };
};
