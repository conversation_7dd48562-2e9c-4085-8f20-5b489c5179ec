import { useState, useCallback } from 'react';
import { PhieuXuatKho } from '@/types/schemas/phieu-xuat-kho.type';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface PhieuXuatKhoResponse {
  results: PhieuXuatKho[];
  count: number;
}

export const usePhieuXuatKho = () => {
  const [phieuXuatKhos, setPhieuXuatKhos] = useState<PhieuXuatKho[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { entity } = useAuth();

  const refreshPhieuXuatKhos = useCallback(async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      const response = await api.get<PhieuXuatKhoResponse>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_XUAT_KHO}/`
      );
      setPhieuXuatKhos(response.data.results || []);
    } catch (error) {
      console.error('Error fetching phieu xuat kho:', error);
      setPhieuXuatKhos([]);
    } finally {
      setIsLoading(false);
    }
  }, [entity?.slug]);

  const addPhieuXuatKho = useCallback(
    async (data: PhieuXuatKho) => {
      if (!entity?.slug) throw new Error('Entity slug is required');

      setIsLoading(true);
      try {
        const response = await api.post<PhieuXuatKho>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_XUAT_KHO}/`,
          data
        );
        await refreshPhieuXuatKhos();
        return response.data;
      } catch (error) {
        console.error('Error adding phieu xuat kho:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, refreshPhieuXuatKhos]
  );

  const updatePhieuXuatKho = useCallback(
    async (uuid: string, data: PhieuXuatKho) => {
      if (!entity?.slug) throw new Error('Entity slug is required');

      setIsLoading(true);
      try {
        const response = await api.patch<PhieuXuatKho>(
          `/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_XUAT_KHO}/${uuid}/`,
          data
        );
        await refreshPhieuXuatKhos();
        return response.data;
      } catch (error) {
        console.error('Error updating phieu xuat kho:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, refreshPhieuXuatKhos]
  );

  const deletePhieuXuatKho = useCallback(
    async (uuid: string) => {
      if (!entity?.slug) throw new Error('Entity slug is required');

      setIsLoading(true);
      try {
        await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.PHIEU_XUAT_KHO}/${uuid}/`);
        await refreshPhieuXuatKhos();
      } catch (error) {
        console.error('Error deleting phieu xuat kho:', error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, refreshPhieuXuatKhos]
  );

  return {
    phieuXuatKhos,
    isLoading,
    addPhieuXuatKho,
    updatePhieuXuatKho,
    deletePhieuXuatKho,
    refreshPhieuXuatKhos
  };
};
