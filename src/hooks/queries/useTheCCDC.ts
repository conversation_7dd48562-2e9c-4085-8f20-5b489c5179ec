import { useState, useCallback } from 'react';
import { TheCCDCItem, TheCCDCResponse, TheCCDCSearchFormValues, UseTheCCDCReturn } from '@/types/schemas';
import api from '@/lib/api';

const generateMockData = (): TheCCDCItem[] => {
  return [
    {
      id: '1',
      stt: 1,
      ma_cc: 'CC001',
      ten_cc: '<PERSON><PERSON>y tính xách tay Dell Latitude 5520',
      ngay_mua: '2024-01-15',
      ngay_kh0: '2024-02-01',
      so_ky_kh: 36,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      ten_bp: 'Phòng Kế toán',
      nh_cc1: 'CC1_001',
      nh_cc2: 'CC2_001',
      nh_cc3: 'CC3_001',
      nuoc_sx: 'Trung Quốc',
      nam_sx: 2023,
      ngay_ct: '2024-01-15',
      so_ct: 'CT001',
      so_hieu_cc: 'SH001',
      ma_nt: 'VND'
    },
    {
      id: '2',
      stt: 2,
      ma_cc: 'CC002',
      ten_cc: 'Máy in laser HP LaserJet Pro M404n',
      ngay_mua: '2024-01-20',
      ngay_kh0: '2024-02-01',
      so_ky_kh: 24,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP002',
      ten_bp: 'Phòng Hành chính',
      nh_cc1: 'CC1_002',
      nh_cc2: 'CC2_002',
      nh_cc3: 'CC3_002',
      nuoc_sx: 'Malaysia',
      nam_sx: 2023,
      ngay_ct: '2024-01-20',
      so_ct: 'CT002',
      so_hieu_cc: 'SH002',
      ma_nt: 'VND'
    },
    {
      id: '3',
      stt: 3,
      ma_cc: 'CC003',
      ten_cc: 'Bàn làm việc gỗ công nghiệp',
      ngay_mua: '2024-02-05',
      ngay_kh0: '2024-03-01',
      so_ky_kh: 60,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP001',
      ten_bp: 'Phòng Kế toán',
      nh_cc1: 'CC1_003',
      nh_cc2: 'CC2_003',
      nh_cc3: 'CC3_003',
      nuoc_sx: 'Việt Nam',
      nam_sx: 2024,
      ngay_ct: '2024-02-05',
      so_ct: 'CT003',
      so_hieu_cc: 'SH003',
      ma_nt: 'VND'
    },
    {
      id: '4',
      stt: 4,
      ma_cc: 'CC004',
      ten_cc: 'Ghế xoay văn phòng có tựa lưng',
      ngay_mua: '2024-02-10',
      ngay_kh0: '2024-03-01',
      so_ky_kh: 48,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP003',
      ten_bp: 'Phòng Kinh doanh',
      nh_cc1: 'CC1_004',
      nh_cc2: 'CC2_004',
      nh_cc3: 'CC3_004',
      nuoc_sx: 'Việt Nam',
      nam_sx: 2024,
      ngay_ct: '2024-02-10',
      so_ct: 'CT004',
      so_hieu_cc: 'SH004',
      ma_nt: 'VND'
    },
    {
      id: '5',
      stt: 5,
      ma_cc: 'CC005',
      ten_cc: 'Máy photocopy Canon iR2006N',
      ngay_mua: '2024-02-15',
      ngay_kh0: '2024-03-01',
      so_ky_kh: 60,
      tk_cc: '211',
      tk_kh: '214',
      tk_cp: '642',
      ma_bp: 'BP002',
      ten_bp: 'Phòng Hành chính',
      nh_cc1: 'CC1_005',
      nh_cc2: 'CC2_005',
      nh_cc3: 'CC3_005',
      nuoc_sx: 'Nhật Bản',
      nam_sx: 2023,
      ngay_ct: '2024-02-15',
      so_ct: 'CT005',
      so_hieu_cc: 'SH005',
      ma_nt: 'VND'
    }
  ];
};

/**
 * Custom hook for managing TheCCDC (CCDC Tool Card) data
 *
 * This hook provides functionality to fetch CCDC tool card data
 * with mock support for testing and development purposes.
 */
export function useTheCCDC(searchParams: TheCCDCSearchFormValues): UseTheCCDCReturn {
  const [data, setData] = useState<TheCCDCItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (searchParams: TheCCDCSearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const mockData = generateMockData();

      // Simulate API call with mock data
      const response = await new Promise<{ data: TheCCDCResponse }>(resolve => {
        setTimeout(() => {
          resolve({
            data: {
              count: mockData.length,
              next: null,
              previous: null,
              results: mockData
            }
          });
        }, 500); // Simulate network delay
      });

      setData(response.data.results);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
      setError(new Error(errorMessage));
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refreshData = useCallback(async () => {
    await fetchData(searchParams);
  }, [fetchData, searchParams]);

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData
  };
}
