export interface PhieuXuatKho {
  uuid?: string;
  ma_gd: string;
  so_ct: string;
  ngay_ct: Date;
  ma_dt: string;
  ten_dt: string;
  dien_giai?: string;
  ma_nt: string;
  ty_gia: number;
  trang_thai: string;
  da_ghi_so?: boolean;
  tong_tien: number;
  tong_sl: number;
  ma_ct: string;
  chi_tiet_xuat: ChiTietPhieuXuatKho[];
}

export interface ChiTietPhieuXuatKho {
  uuid?: string;
  ma_vt?: string;
  ten_vt?: string;
  ma_dvt?: string;
  ma_kho?: string;
  ton_kho?: number;
  so_luong?: number;
  dich_danh?: boolean;
  don_gia?: number;
  thanh_tien?: number;
  loai_ts_cc?: string;
  tk_co?: string;
  ly_do_xuat?: string;
  tk_no?: string;
  ma_bp?: string;
  ma_vu_viec?: string;
  dot_tt?: string;
  ma_hd?: string;
  phi?: number;
  ma_sp?: string;
  ma_lenh_sx?: string;
  don_gia_vnd?: number;
  thanh_tien_vnd?: number;
  sl_da_xuat?: number;
  so_px_thuc_te?: string;
  dong_px?: string;
  so_phieu_yc?: string;
  dong_yc?: string;
  line?: number; // Line number from API

  // Related data objects
  vat_tu_data?: {
    ma_vt: string;
    ten_vt: string;
  };
  don_vi_tinh_data?: {
    uuid?: string;
    ma_dvt: string;
    ten_dvt: string;
  };
  kho_data?: {
    ma_kho: string;
    ten_kho: string;
  };

  ly_do_xuat_data?: {
    ma_ly_do: string;
    ten_ly_do: string;
  };

  tai_khoan_no_data?: {
    ma_tk: string;
    ten_tk: string;
  };

  bo_phan_data?: {
    ma_bp: string;
    ten_bp: string;
  };

  // Additional fields for columns
  ma_khe_uoc?: string;
  chi_phi_khong_hop_le?: string;

  // For storing original API data during transformation
  _originalData?: any;
}
