import type { CongCuDungCu } from './cong-cu-dung-cu.type';
import type { <PERSON><PERSON><PERSON><PERSON> } from './account.type';
import type { ApiResponse } from '../api.type';
import type { <PERSON><PERSON><PERSON> } from './bo-phan.type';

export interface DieuChinhBoPhanSuDungCCDC {
  /**
   * UUID of the record
   */
  uuid: string;

  /**
   * The entity this adjustment belongs to
   */
  entity_model: string;

  /**
   * Accounting period for this adjustment
   */
  ky: number;

  /**
   * Accounting year for this adjustment
   */
  nam: number;

  /**
   * Tool information declaration this adjustment applies to
   */
  ma_cc?: string | null;

  /**
   * Full data of the tool
   */
  ma_cc_data?: CongCuDungCu | null;

  /**
   * Department for tool usage this adjustment applies to
   */
  ma_bp?: string | null;

  /**
   * Full data of the department
   */
  ma_bp_data?: BoPhan | null;

  /**
   * Cost center account for this adjustment
   */
  tk_cc?: string | null;
  tk_cc_data?: TaiKhoan | null;

  /**
   * Customer account for this adjustment
   */
  tk_kh?: string | null;
  tk_kh_data?: Tai<PERSON><PERSON>an | null;

  /**
   * Expense account for this adjustment
   */
  tk_cp?: string | null;
  tk_cp_data?: TaiKhoan | null;
}

export type DieuChinhBoPhanSuDungCCDCInput = Omit<
  DieuChinhBoPhanSuDungCCDC,
  'uuid' | 'entity_model' | 'ma_cc_data' | 'ma_bp_data' | 'tk_cc_data' | 'tk_kh_data' | 'tk_cp_data'
>;

/**
 * Type for DieuChinhBoPhanSuDungCCDC API response
 */
export type DieuChinhBoPhanSuDungCCDCResponse = ApiResponse<DieuChinhBoPhanSuDungCCDC>;
