// TypeScript interfaces for BaoCaoTongHopHangBanTraLai (Comprehensive Sales Return Report)

export interface BaoCaoTongHopHangBanTraLaiItem {
  id: string;
  nhom: string;
  ma_vt: string;
  ten_vt: string;
  dvt: string;
  sl_nhap: number;
  gia2: number;
  tien2: number;
  thue: number;
  pt: number;
  gia: number;
  tien_nhap: number;
  tien_lai: number;
  isTotal?: boolean;
}

export interface BaoCaoTongHopHangBanTraLaiResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoTongHopHangBanTraLaiItem[];
}

export interface BaoCaoTongHopHangBanTraLaiSearchFormValues {
  ngay_ct1: string;
  ngay_ct2: string;
  so_ct1?: string;
  so_ct2?: string;
  ma_nvbh?: string;
  ma_kh?: string;
  nh_kh1?: string;
  nh_kh2?: string;
  nh_kh3?: string;
  rg_code?: string;
  ma_vt?: string;
  ma_lvt?: string;
  ton_kho_yn?: boolean;
  nh_vt1?: string;
  nh_vt2?: string;
  nh_vt3?: string;
  ma_kho?: string;
  mau_bc?: string;
  group_by?: string;
  ma_gd?: string;
  tk_vt?: string;
  tk_dt?: string;
  tk_gv?: string;
  ma_lo?: string;
  ma_vi_tri?: string;
  dien_giai?: string;
  reportFilterTemplate?: string;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseBaoCaoTongHopHangBanTraLaiReturn {
  data: BaoCaoTongHopHangBanTraLaiItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoTongHopHangBanTraLaiSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
