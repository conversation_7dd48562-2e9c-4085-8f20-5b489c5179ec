import { z } from 'zod';

// Balance Sheet Multi Periods Data Item Interface
export interface BangCanDoiKeToanItem {
  id: string;
  ma_so: string;
  chi_tieu: string;
  systotal: number;
  [key: string]: any; // For dynamic period columns
}

// API Response Interface
export interface BangCanDoiKeToanResponse {
  results: BangCanDoiKeToanItem[];
  total: number;
}

// Hook Return Type Interface
export interface UseBangCanDoiKeToanReturn {
  data: BangCanDoiKeToanItem[];
  isLoading: boolean;
  error: Error | null;
  searchParams: any;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}

// Form Field Options Types
export interface TimeTypeOption {
  label: string;
  value: 'month' | 'quarter' | 'halfYear' | 'year';
}

export interface ReportTemplateOption {
  label: string;
  value: string;
}

export interface AnalysisTemplateOption {
  label: string;
  value: string;
}

// Constants for form options
export const TIME_TYPE_OPTIONS: TimeTypeOption[] = [
  { label: 'Tháng', value: 'month' },
  { label: 'Quý', value: 'quarter' },
  { label: 'Nửa năm', value: 'halfYear' },
  { label: 'Năm', value: 'year' }
];

export const REPORT_TEMPLATE_OPTIONS: ReportTemplateOption[] = [
  {
    label: 'Bảng cân đối kế toán - Thông tư 200',
    value: 'balance_sheet_tt200'
  }
];

export const CURRENCY_TEMPLATE_OPTIONS: ReportTemplateOption[] = [
  { label: 'Mẫu tiền chuẩn', value: 'standard_currency' },
  { label: 'Mẫu tiền nt', value: 'foreign_currency' }
];

export const ANALYSIS_TEMPLATE_OPTIONS: AnalysisTemplateOption[] = [
  {
    label: 'Bảng cân đối kế toán theo đơn vị',
    value: 'balance_sheet_unit'
  }
];
