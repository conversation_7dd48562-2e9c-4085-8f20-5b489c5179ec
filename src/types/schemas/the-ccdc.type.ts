// TypeScript interfaces for TheCCDC (CCDC Tool Card)

export interface TheCCDCItem {
  id: string;
  stt: number;
  ma_cc: string;
  ten_cc: string;
  ngay_mua: string;
  ngay_kh0: string;
  so_ky_kh: number;
  tk_cc: string;
  tk_kh: string;
  tk_cp: string;
  ma_bp: string;
  ten_bp?: string;
  nh_cc1?: string;
  nh_cc2?: string;
  nh_cc3?: string;
  nuoc_sx?: string;
  nam_sx?: number;
  ngay_ct?: string;
  ngay_giam?: string;
  so_ct?: string;
  so_ct_giam?: string;
  ly_do_giam?: string;
  ngay_dc?: string;
  ly_do_dc?: string;
  so_hieu_cc?: string;
  ma_nt?: string;
}

export interface TheCCDCResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: TheCCDCItem[];
}

export interface TheCCDCSearchFormValues {
  ky?: number;
  nam?: number;
  ma_cc?: string;
  ma_lcc?: string;
  ma_bp?: string;
  nh_cc1?: string;
  nh_cc2?: string;
  nh_cc3?: string;
  ma_unit?: string;
  mau_bc?: number;
  data_analysis_struct?: string;
  [key: string]: any;
}

export interface UseTheCCDCReturn {
  data: TheCCDCItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: TheCCDCSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
