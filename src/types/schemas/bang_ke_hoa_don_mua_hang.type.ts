/**
 * Schema definitions for Bang Ke Hoa Don Mu<PERSON> (Purchase Invoice List Report)
 *
 * This file contains all type definitions related to purchase invoice list reporting,
 * including data structures for individual items, API responses, and hook return types.
 */

export interface BangKeHoaDonItem {
  id: string;
  unit_id: string;
  ma_ct: string;
  ngay_ct: string;
  so_ct: string;
  ma_kh: string;
  ma_vt: string;
  ma_kho: string;
  dvt: string;
  he_so: number;
  sl_nhap: number;
  gia: number;
  tien_nhap: number;
  ck: number;
  cp: number;
  thue: number;
  dien_giai: string;
  tk_vt: string;
  tk_du: string;
  so_ct0: string;
  ngay_ct0: string;
  ma_bp: string;
  ma_vv: string;
  ma_hd: string;
  ma_ku: string;
  ma_phi: string;
  ma_sp: string;
  ma_lsx: string;
  ma_dtt: string;
  ma_cp0: string;
  ma_unit: string;
  ngay_ct3: string;
  so_ct3: string;
  ten_kh: string;
  ten_vt: string;
  tien0: number;
  tong_tt: number;
}

export interface BangKeHoaDonResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BangKeHoaDonItem[];
}

export interface UseBangKeHoaDonReturn {
  data: BangKeHoaDonItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: () => Promise<void>;
}
