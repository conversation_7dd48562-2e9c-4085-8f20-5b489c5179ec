// TypeScript interfaces for BaoCaoLuuChuyenTienTeTrucTiep (Direct Cash Flow Report)

export interface BaoCaoLuuChuyenTienTeTrucTiepItem {
  id?: string;
  ma_so: string;
  chi_tieu: string;
  thuyet_minh: string;
  tk: string;
  ky_nay: string;
  ky_truoc: string;
  ky_nay_nt?: string;
  ky_truoc_nt?: string;
  luy_ke_ky_nay?: string;
  luy_ke_ky_truoc?: string;
  luy_ke_ky_nay_nt?: string;
  luy_ke_ky_truoc_nt?: string;
  xchi_tieu: string;
  xchi_tieu2: string;
  tk_du: string;
  dau_cuoi: string;
}

export interface BaoCaoLuuChuyenTienTeTrucTiepResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: BaoCaoLuuChuyenTienTeTrucTiepItem[];
}

export interface BaoCaoLuuChuyenTienTeTrucTiepSearchFormValues {
  ngay_ct11?: string;
  ngay_ct12?: string;
  ngay_ct01?: string;
  ngay_ct02?: string;
  nguoi_lap?: string;
  ma_unit?: string;
  ten_unit?: string;
  id_maubc?: string;
  mau_bc?: string;
  [key: string]: any;
}

export interface UseBaoCaoLuuChuyenTienTeTrucTiepReturn {
  data: BaoCaoLuuChuyenTienTeTrucTiepItem[];
  isLoading: boolean;
  error: Error | null;
  fetchData: (searchParams: BaoCaoLuuChuyenTienTeTrucTiepSearchFormValues) => Promise<void>;
  refreshData: () => Promise<void>;
}
