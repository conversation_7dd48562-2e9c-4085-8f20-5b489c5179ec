import React from 'react';
import AritoSearchModal from '@/components/custom/arito/arito-search-modal';

interface SearchModalProps extends React.ComponentProps<typeof AritoSearchModal> {
  items?: any[];
}

const SearchModal: React.FC<SearchModalProps> = ({ items, ...props }) => {
  React.useEffect(() => {
    if (items && items.length > 0) {
      // Set mock data to local storage to simulate API response
      localStorage.setItem(props.docType || 'mockData', JSON.stringify(items));
    }
  }, [items, props.docType]);

  return <AritoSearchModal {...props} />;
};

export default SearchModal;
