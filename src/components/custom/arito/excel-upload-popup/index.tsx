import { UploadIcon, RefreshCcwIcon, CheckCircleIcon, XCircleIcon, MinusIcon, XIcon, FileIcon } from 'lucide-react';
import { GridColDef } from '@mui/x-data-grid';
import React, { useState } from 'react';
import Image from 'next/image';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { triggerFileInput } from './handle-excel';
import { Button } from '@/components/ui/button';

const ExcelImportPopup = ({
  columns,
  doctype,
  isOpen,
  onClose,
  handleUpload
}: {
  columns: GridColDef[];
  doctype: string;
  isOpen: boolean;
  onClose: () => void;
  handleUpload: (file: File) => void;
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [sheet, setSheet] = useState('Mặc định');
  const [writeOption, setWriteOption] = useState('Cảnh báo và dừng nếu dữ liệu đã tồn tại');

  if (!isOpen) return null;

  const handleFileClick = () => {
    triggerFileInput(columns, doctype, setUploadedFile);
  };

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30'>
      <div className='w-[800px] rounded border border-[#cfd8dc] bg-[#f8f9fa] text-[12px] shadow-lg'>
        {/* Header */}
        <div className='flex h-[36px] items-center justify-between border-b border-[#cfd8dc] bg-[#e6f0f8] px-2'>
          <div className='flex items-center space-x-2 font-semibold text-[#333]'>
            <Image src='/excel-icon.png' alt='Excel Icon' width={18} height={18} className='h-[18px] w-[18px]' />
            <span>Lấy dữ liệu từ Excel</span>
          </div>
          <div className='flex items-center space-x-1'>
            <Button className='size-5 text-[#333] hover:bg-gray-200' size='icon' variant='ghost'>
              <MinusIcon className='h-[12px] w-[12px]' />
            </Button>
            <Button className='size-5 text-[#333] hover:bg-gray-200' size='icon' variant='ghost' onClick={onClose}>
              <XIcon className='h-[12px] w-[12px]' />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className='space-y-2 p-3'>
          {/* Tệp đính kèm */}
          <div className='flex items-center gap-2'>
            <label className='w-[100px] text-left text-sm font-medium text-[#333]'>Tệp đính kèm</label>
            <div className='flex flex-grow items-center gap-2'>
              {/* Chọn tệp button */}
              <Button
                onClick={handleFileClick}
                size='icon'
                className='size-7 bg-[#007bff] text-white hover:bg-[#0069d9]'
              >
                <UploadIcon className='h-[16px] w-[16px]' />
              </Button>

              {/* File name display */}
              <div className='flex h-[28px] flex-grow items-center border-b border-[#cfd8dc] bg-[#f8f9fa] px-2 text-left text-sm text-[#333]'>
                {uploadedFile ? (
                  <span className='truncate'>{uploadedFile.name}</span>
                ) : (
                  <span className='italic text-gray-500'></span>
                )}
              </div>

              {/* Thư viện button */}
              <Button
                onClick={() => alert('Mở thư viện')}
                size='icon'
                className='size-7 border border-[#cfd8dc] bg-[#f1f1f1] text-[#333] hover:bg-gray-200'
              >
                <FileIcon className='h-[16px] w-[16px]' />
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <Tabs defaultValue='options'>
            <TabsList className='flex h-[30px] border-b border-[#cfd8dc]'>
              <TabsTrigger
                value='options'
                className='h-full border-b-2 border-[#009688] px-4 py-0 text-[12px] font-semibold text-[#009688]'
              >
                Tùy chọn
              </TabsTrigger>
              <TabsTrigger value='others' className='h-full px-4 py-0 text-[12px] text-[#333]'>
                Khác
              </TabsTrigger>
            </TabsList>

            {/* Tùy chọn Content */}
            <TabsContent value='options' className='space-y-2 pt-2'>
              {/* Sheet */}
              <div className='flex items-center gap-2'>
                <label className='w-[100px] text-left text-[#333]'>Sheet</label>
                <div className='flex flex-grow items-center gap-2'>
                  <select
                    value={sheet}
                    onChange={e => setSheet(e.target.value)}
                    className='h-[28px] w-full rounded-none border-0 border-b border-[#cfd8dc] bg-[#f8f9fa] px-2 text-left text-[12px] focus:outline-none'
                  >
                    <option value='Mặc định'>Mặc định</option>
                    <option value='Sheet 1'>Sheet 1</option>
                    <option value='Sheet 2'>Sheet 2</option>
                  </select>

                  {/* Refresh button */}
                  <Button size='icon' variant='ghost' className='size-7 text-[#007bff] hover:bg-gray-200'>
                    <RefreshCcwIcon className='h-[14px] w-[14px]' />
                  </Button>
                </div>
              </div>

              {/* Tùy chọn ghi */}
              <div className='flex items-center gap-2'>
                <label className='w-[100px] text-left text-[#333]'>Tùy chọn ghi</label>
                <div className='flex flex-grow items-center'>
                  <select
                    value={writeOption}
                    onChange={e => setWriteOption(e.target.value)}
                    className='h-[28px] w-full rounded-none border-0 border-b border-[#cfd8dc] bg-[#f8f9fa] px-2 text-left text-[12px] focus:outline-none'
                  >
                    <option value='Cảnh báo và dừng nếu dữ liệu đã tồn tại'>
                      Cảnh báo và dừng nếu dữ liệu đã tồn tại
                    </option>
                    <option value='Cập nhật lại nếu dữ liệu đã tồn tại'>Cập nhật lại nếu dữ liệu đã tồn tại</option>
                    <option value='Bỏ qua các dòng dữ liệu đã tồn tại'>Bỏ qua các dòng dữ liệu đã tồn tại</option>
                  </select>
                </div>
              </div>
            </TabsContent>

            {/* Khác Content */}
            <TabsContent value='others' className='pt-2 text-[#666]'>
              Nội dung khác có thể được thêm vào đây...
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className='flex items-center justify-end space-x-2 border-t border-[#cfd8dc] bg-[#f8f9fa] px-3 py-2'>
          <Button
            onClick={onClose}
            className='flex h-[28px] items-center space-x-1 border border-[#d32f2f] bg-white px-3 text-[12px] text-[#d32f2f] shadow hover:bg-red-50'
          >
            <XCircleIcon className='h-[14px] w-[14px]' />
            <span>Hủy</span>
          </Button>
          <Button
            onClick={() => {
              if (uploadedFile) handleUpload(uploadedFile);
            }}
            className='flex h-[28px] items-center space-x-1 bg-[#009688] px-3 text-[12px] text-white shadow hover:bg-teal-700'
            disabled={!uploadedFile}
          >
            <CheckCircleIcon className='h-[14px] w-[14px]' />
            <span>Đồng ý</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ExcelImportPopup;
