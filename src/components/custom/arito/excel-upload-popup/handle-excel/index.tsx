import { GridColDef } from '@mui/x-data-grid';
import ExcelJS from 'exceljs';
import * as yup from 'yup';
import { validateRow } from './validate-row';

export const getColumnsFromSchema = (schema: yup.ObjectSchema<any>): string[] => {
  const schemaDescription = schema.describe();
  return Object.keys(schemaDescription.fields);
};

// Function to get column names with required indicator
const getColumnNames = (
  excelColumns: GridColDef[],
  schemaColumns: string[]
): (string | ExcelJS.CellRichTextValue)[] => {
  return excelColumns.map(col => {
    const isRequired = schemaColumns.includes(col.field);
    return isRequired
      ? {
          richText: [{ text: col.headerName || '' }, { text: '*', font: { color: { argb: 'FFFF0000' } } }]
        }
      : col.headerName || '';
  });
};

// Function to set column widths
const setColumnWidths = (worksheet: ExcelJS.Worksheet, excelColumns: GridColDef[]) => {
  excelColumns.forEach((col, index) => {
    worksheet.getColumn(index + 1).width = col.width ? col.width / 10 : 10;
  });
};

// Function to create and download Excel file
const createAndDownloadExcel = async (workbook: ExcelJS.Workbook, fileName: string) => {
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: 'application/octet-stream' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  link.click();
};

// Function to generate Excel file
const generateExcel = async (
  excelColumns: GridColDef[],
  data: any[] = [],
  doctype: string,
  schema: yup.ObjectSchema<any>,
  isSample: boolean = false
) => {
  const fields = excelColumns.map(col => col.field);
  const schemaColumns = getColumnsFromSchema(schema);
  const columnNames = getColumnNames(excelColumns, schemaColumns);

  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Sheet1');

  // Add header row
  const headerRow = worksheet.addRow(columnNames);
  headerRow.eachCell((cell, colNumber) => {
    const col = excelColumns[colNumber - 1];
    const isRequired = schemaColumns.includes(col.field);
    if (isRequired) {
      cell.value = {
        richText: [{ text: col.headerName || '' }, { text: '*', font: { color: { argb: 'FFFF0000' } } }]
      };
    } else {
      cell.value = col.headerName;
    }
  });

  // Add data rows if not sample
  if (!isSample) {
    data.forEach(row => {
      worksheet.addRow(fields.map(field => row[field]));
    });
  }

  // Set column widths
  setColumnWidths(worksheet, excelColumns);

  const fileName = `${doctype}-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.xlsx`;
  await createAndDownloadExcel(workbook, fileName);
};

export const handleExport = async (
  excelColumns: GridColDef[],
  data: any[] = [],
  schema: any,
  doctype: string = 'export'
) => {
  await generateExcel(excelColumns, data, doctype, schema);
};

export const sampleExcel = async (
  excelColumns: GridColDef[],
  schema: yup.ObjectSchema<any>,
  doctype: string = 'sample'
) => {
  await generateExcel(excelColumns, [], doctype, schema, true);
};

// Function to handle import from Excel file
export const handleImport = async (
  file: File,
  excelColumns: GridColDef[],
  doctype: string,
  option: string,
  sheet: string | null,
  id_column: string,
  maxError: number = 200,
  schema: yup.ObjectSchema<any>,
  validation: (
    data: { [key: string]: any }[],
    schema: yup.ObjectSchema<any>
  ) => { type: string; message: string; position: string }[],
  editData: (data: { [key: string]: any }[]) => { [key: string]: any }[],
  onImportFile: (
    data: { [key: string]: any }[],
    id_column: string,
    doctype: string,
    option: string,
    maxError: number,
    schema: yup.ObjectSchema<any>,
    validation: (
      data: { [key: string]: any }[],
      schema: yup.ObjectSchema<any>
    ) => { type: string; message: string; position: string }[]
  ) => void = importFile
) => {
  const workbook = new ExcelJS.Workbook();

  const arrayBuffer = await file.arrayBuffer();
  await workbook.xlsx.load(arrayBuffer);
  let worksheet: ExcelJS.Worksheet | undefined = workbook.worksheets[0];
  if (sheet) {
    worksheet = workbook.getWorksheet(sheet);
  }
  if (!worksheet) {
    throw new Error('Worksheet not found');
  }

  const fields = excelColumns.map(col => col.field);
  let importedData: { [key: string]: any }[] = [];

  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber === 1) return; // Skip header row
    const rowData: any = {};
    row.eachCell((cell, colNumber) => {
      const field = fields[colNumber - 1];
      rowData[field] = cell.value;
    });
    importedData.push(rowData);
  });
  console.log(importedData);
  importedData = editData(importedData);
  onImportFile(importedData, id_column, doctype, option, maxError, schema, validation);
  window.location.reload();
};

// Function to trigger file input and handle file selection
export const triggerFileInput = (excelColumns: GridColDef[], doctype: string, onUploadedFile: (file: File) => void) => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx, .xls';
  input.onchange = async (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      onUploadedFile(file);
    }
  };
  input.click();
};

export async function importFile(
  data: { [key: string]: any }[],
  id_column: string,
  doctype: string,
  option: string,
  maxError: number,
  schema: yup.ObjectSchema<any>,
  validation: (
    data: { [key: string]: any }[],
    schema: yup.ObjectSchema<any>
  ) => { type: string; message: string; position: string }[]
) {
  // Placeholder implementation - ERPNext functionality removed
  const objs: any[] = [];
  let errs = validation(data, schema);
  if (errs.length) {
    console.log(errs);
    return;
  }
  console.log('Import functionality disabled - ERPNext removed');
  console.log(errs);
}

export const validation = (data: { [key: string]: any }[], schema: yup.ObjectSchema<any>) => {
  let err = [];
  for (let i = 0; i < data.length; i++) {
    const rowErrs = validateRow({ data: data[i], schema: schema, rowNumber: i });
    err.push(...rowErrs);
  }
  return err;
};
