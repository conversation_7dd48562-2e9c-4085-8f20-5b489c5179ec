import { Button } from '@mui/material';
import { AritoDialog, AritoIcon } from '@/components/custom/arito';

export interface ConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  iconCode?: number;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disableBackdropClose?: boolean;
  disableEscapeKeyDown?: boolean;
  selectedObj?: any | null;
}

function ConfirmationDialog({
  open,
  onClose,
  onConfirm,
  title = 'Cảnh báo',
  message = 'Bạn có chắc chắn không?',
  iconCode = 260,
  maxWidth = 'lg',
  disableBackdropClose = true,
  disableEscapeKeyDown = true,
  selectedObj
}: ConfirmationDialogProps) {
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={title}
      maxWidth={maxWidth}
      disableBackdropClose={disableBackdropClose}
      disableEscapeKeyDown={disableEscapeKeyDown}
      titleIcon={<AritoIcon icon={iconCode} />}
      actions={
        <>
          <Button
            className='!hover:bg-main/80 !bg-main text-white'
            onClick={onConfirm}
            type='submit'
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            Đồng ý
          </Button>
          <Button onClick={onClose} variant='outlined'>
            <AritoIcon icon={885} marginX='4px' />
            Hủy
          </Button>
        </>
      }
    >
      <p className='min-h-20 min-w-96 p-4 text-base font-medium'>{message}</p>
    </AritoDialog>
  );
}

export default ConfirmationDialog;
