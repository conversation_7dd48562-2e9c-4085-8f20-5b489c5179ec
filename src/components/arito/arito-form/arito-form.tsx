import { Default<PERSON><PERSON><PERSON>, FormProvider, useForm } from 'react-hook-form';
import { createContext, ReactNode, useState } from 'react';
import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Tab, Tabs } from '@mui/material';
import { LogOut, Save } from 'lucide-react';
import * as yup from 'yup';
import { AritoActionButton } from '@/components/custom/arito';
import { AritoActionBar } from '@/components/custom/arito';

// Create a context to expose form control and errors
export const AritoFormContext = createContext<any>(null);

export type TabItem = {
  id: string;
  label: string;
  component: ReactNode;
};

export interface AritoFormProps<TFormData extends Record<string, any>> {
  mode?: 'view' | 'edit' | 'add';
  subTitle?: string;
  initialData?: Partial<TFormData>;
  onSubmit?: (data: TFormData) => void;
  schema?: yup.ObjectSchema<any>;
  headerFields?: ReactNode | TabItem[]; // Can be either simple ReactNode or TabItem array
  tabs?: ReactNode | TabItem[]; // Make tabs required - this is the standard approach
  onClose?: () => void;
  from?: ReactNode;
  bottomBar?: ReactNode;
}

export const AritoForm = <TFormData extends Record<string, any>>({
  mode = 'edit',
  subTitle,
  initialData,
  onSubmit,
  schema,
  headerFields,
  tabs,
  onClose,
  from,
  bottomBar
}: AritoFormProps<TFormData>) => {
  const [activeTab, setActiveTab] = useState<string>(Array.isArray(tabs) ? tabs[0]?.id || '' : '');
  const [activeHeaderTab, setActiveHeaderTab] = useState<string>(
    Array.isArray(headerFields) ? headerFields[0]?.id || '' : ''
  );
  const [isExiting, setIsExiting] = useState(false);

  const methods = useForm<TFormData>({
    defaultValues: initialData as DefaultValues<TFormData>,
    mode: 'onSubmit',
    ...(schema && { resolver: yupResolver(schema) })
  });

  const {
    handleSubmit,
    formState: { errors },
    control
  } = methods;

  const isViewMode = mode === 'view';
  const headerHasTabs = Array.isArray(headerFields);
  const tabsHasTabs = Array.isArray(tabs);

  const handleCloseWithAnimation = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose?.();
      setIsExiting(false);
    }, 300);
  };

  const onFormSubmit = (data: TFormData) => {
    onSubmit?.(data);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  const handleHeaderTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveHeaderTab(newValue);
  };

  // Add a unique form ID
  const formId = 'arito-form';

  return (
    <FormProvider {...methods}>
      <AritoFormContext.Provider value={{ control, errors, isViewMode }}>
        <div className='flex w-screen flex-col overflow-hidden bg-white lg:fixed lg:h-full'>
          <div
            className={`flex flex-1 flex-col bg-white shadow-lg ${
              isExiting ? 'animate-form-exit' : 'animate-form-appear'
            }`}
          >
            <AritoActionBar
              inForm={true}
              className='border-b border-gray-300'
              titleComponent={
                <div className='flex flex-col items-center justify-center lg:items-start lg:justify-start'>
                  <h1 className='text-xl font-bold'>{mode == 'add' ? 'Mới' : mode === 'view' ? 'Xem' : 'Sửa'}</h1>
                  <h2 className='text-xs text-gray-500'>{subTitle}</h2>
                </div>
              }
            >
              {!isViewMode && (
                <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' form={formId} />
              )}
              <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={handleCloseWithAnimation} />
            </AritoActionBar>

            {from}

            <div className='flex items-center justify-between border-t-2 border-gray-300'></div>

            <form
              id={formId}
              onSubmit={handleSubmit(onFormSubmit)}
              className='flex flex-1 flex-col gap-y-6 overflow-auto'
            >
              {/* Header fields - always visible */}
              {headerFields && (
                <div className='border-none'>
                  {headerHasTabs ? (
                    <>
                      {/* Header Tabs Navigation - MUI Tabs */}
                      <Box
                        sx={{
                          borderBottom: 1,
                          borderColor: 'divider',
                          height: '32px',
                          display: 'flex',
                          alignItems: 'center',
                          textTransform: 'none',
                          '& .MuiTabs-root': {
                            minHeight: '32px'
                          },
                          '& .MuiTab-root': {
                            minHeight: '32px',
                            padding: '6px 16px',
                            fontWeight: '1000',
                            fontSize: '0.875rem',
                            '&.Mui-selected': {
                              color: '#2563EB',
                              borderBottom: '1px solid #0b87c9'
                            }
                          },
                          '& .MuiTabs-indicator': {
                            backgroundColor: '#0b87c9'
                          }
                        }}
                      >
                        <Tabs
                          value={activeHeaderTab}
                          onChange={handleHeaderTabChange}
                          variant='scrollable'
                          scrollButtons='auto'
                          textColor='primary'
                          indicatorColor='primary'
                        >
                          {(headerFields as TabItem[]).map(tab => (
                            <Tab
                              key={tab.id}
                              value={tab.id}
                              label={tab.label}
                              sx={{
                                textTransform: 'none'
                              }}
                            />
                          ))}
                        </Tabs>
                      </Box>

                      {/* Header Tab Content */}
                      <Box>
                        {(headerFields as TabItem[]).map(tab => (
                          <div key={tab.id} className={activeHeaderTab === tab.id ? 'block' : 'hidden'}>
                            {tab.component}
                          </div>
                        ))}
                      </Box>
                    </>
                  ) : (
                    // Regular non-tabbed header content
                    <Box>{headerFields}</Box>
                  )}
                </div>
              )}

              {tabs && (
                <div className='border-none'>
                  {tabsHasTabs ? (
                    <>
                      {/* Main Tabs Navigation - MUI Tabs */}
                      <Box
                        sx={{
                          borderBottom: 1,
                          borderColor: 'divider',
                          height: '50px',
                          overflowY: 'scroll',
                          overflowX: 'hidden',
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          textTransform: 'none',
                          '& .MuiTabs-root': {
                            minHeight: '32px'
                          },
                          '& .MuiTab-root': {
                            minHeight: '32px',
                            padding: '2px 16px',
                            fontWeight: '1000',
                            fontSize: '11px',
                            '&.Mui-selected': {
                              color: '#0b87c9',
                              borderBottom: '1px solid #0b87c9'
                            }
                          },
                          '& .MuiTabs-indicator': {
                            backgroundColor: '#0b87c9'
                          }
                        }}
                      >
                        <Tabs
                          value={activeTab}
                          onChange={handleTabChange}
                          variant='scrollable'
                          scrollButtons='auto'
                          textColor='primary'
                          indicatorColor='primary'
                        >
                          {tabs.map(tab => (
                            <Tab
                              key={tab.id}
                              value={tab.id}
                              label={tab.label}
                              sx={{
                                textTransform: 'none'
                              }}
                            />
                          ))}
                        </Tabs>
                      </Box>

                      {/* Tab Content */}
                      <Box>
                        {tabs.map(tab => (
                          <div key={tab.id} className={activeTab === tab.id ? 'block' : 'hidden'}>
                            {tab.component}
                          </div>
                        ))}
                      </Box>
                    </>
                  ) : (
                    // Regular non-tabbed header content
                    <Box>{tabs}</Box>
                  )}
                </div>
              )}
            </form>
          </div>
          {bottomBar}
        </div>
      </AritoFormContext.Provider>

      <style jsx global>{`
        @keyframes formAppear {
          from {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
          }
          to {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }

        @keyframes formExit {
          from {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
          to {
            opacity: 0;
            transform: scale(0.95) translateY(-10px);
          }
        }

        .animate-form-appear {
          animation: formAppear 0.3s ease-out forwards;
        }

        .animate-form-exit {
          animation: formExit 0.3s ease-in forwards;
        }
      `}</style>
    </FormProvider>
  );
};
