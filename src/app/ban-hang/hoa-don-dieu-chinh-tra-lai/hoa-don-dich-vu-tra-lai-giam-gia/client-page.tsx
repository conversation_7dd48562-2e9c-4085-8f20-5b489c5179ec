'use client';

import React from 'react';
import { serviceReturnInvoiceSchema, serviceReturnInvoiceInitialValues } from './schemas';
import { serviceReturnInvoiceColumns, serviceReturnInvoiceItemColumns } from './columns';

import AritoColoredDot from '@/components/custom/arito/icon/colored-dot';
import { generalTabs, detailedTabs } from './fields';

interface ServiceReturnInvoiceClientPageProps {
  invoices: any[];
}

export function ServiceReturnInvoiceClientPage({ invoices }: ServiceReturnInvoiceClientPageProps) {
  const configs: any[] = [
    {
      title: 'Hóa đơn dịch vụ trả lại, giảm giá',
      docType: 'Service Return Invoice',
      popupConfig: {
        generalTabs: generalTabs,
        detailedTabs: detailedTabs,
        validationSchema: serviceReturnInvoiceSchema,
        initialValues: serviceReturnInvoiceInitialValues
      },
      tableConfig: {
        name: '<PERSON><PERSON><PERSON> cả',
        rows: invoices,
        columns: serviceReturnInvoiceColumns
      },
      subTableField: 'items',
      subTableColumns: serviceReturnInvoiceItemColumns
    },
    {
      title: 'Hóa đơn dịch vụ trả lại, giảm giá',
      docType: 'Service Return Invoice',
      popupConfig: {
        generalTabs: generalTabs,
        detailedTabs: detailedTabs,
        validationSchema: serviceReturnInvoiceSchema,
        initialValues: serviceReturnInvoiceInitialValues
      },
      tableConfig: {
        name: 'Chưa ghi số',
        icon: <AritoColoredDot color='pink' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.status === 'Chưa ghi số'),
        columns: serviceReturnInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: serviceReturnInvoiceItemColumns
    },
    {
      title: 'Hóa đơn dịch vụ trả lại, giảm giá',
      docType: 'Service Return Invoice',
      popupConfig: {
        generalTabs: generalTabs,
        detailedTabs: detailedTabs,
        validationSchema: serviceReturnInvoiceSchema,
        initialValues: serviceReturnInvoiceInitialValues
      },
      tableConfig: {
        name: 'Chờ duyệt',
        icon: <AritoColoredDot color='red' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.status === 'Chờ duyệt'),
        columns: serviceReturnInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: serviceReturnInvoiceItemColumns
    },
    {
      title: 'Hóa đơn dịch vụ trả lại, giảm giá',
      docType: 'Service Return Invoice',
      popupConfig: {
        generalTabs: generalTabs,
        detailedTabs: detailedTabs,
        validationSchema: serviceReturnInvoiceSchema,
        initialValues: serviceReturnInvoiceInitialValues
      },
      tableConfig: {
        name: 'Đã ghi số',
        icon: <AritoColoredDot color='blue' className='m-1.5' />,
        rows: invoices.filter(invoice => invoice.trangThai === 'Đã ghi số'),
        columns: serviceReturnInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: serviceReturnInvoiceItemColumns
    },
    {
      title: 'Hóa đơn dịch vụ trả lại, giảm giá',
      docType: 'Service Return Invoice',
      popupConfig: {
        generalTabs: generalTabs,
        detailedTabs: detailedTabs,
        validationSchema: serviceReturnInvoiceSchema,
        initialValues: serviceReturnInvoiceInitialValues
      },
      tableConfig: {
        name: 'Khác',
        icon: <AritoColoredDot color='black' className='m-1.5' />,
        rows: invoices.filter(
          invoice =>
            invoice.status !== 'Lập chứng từ' && invoice.status !== 'Chờ duyệt' && invoice.trangThai !== 'Trả lại'
        ),
        columns: serviceReturnInvoiceColumns,
        tabProps: { className: 'whitespace-nowrap' }
      },
      subTableField: 'items',
      subTableColumns: serviceReturnInvoiceItemColumns
    }
  ];

  return (
    <div className='p-4'>
      <h1 className='mb-4 text-2xl font-bold'>Hóa đơn dịch vụ trả lại, giảm giá</h1>
      <p>Component needs to be implemented with custom components</p>
    </div>
  );
}
