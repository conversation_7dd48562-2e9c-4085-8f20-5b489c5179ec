import { Inter, Noto_Sans } from 'next/font/google';
import { getLocale } from 'next-intl/server';
import { cookies } from 'next/headers';
import type { Metadata } from 'next';
import { AritoAppBar } from '@/components/arito/arito-app-bar';
import { Footer } from '@/components/layout/footer';
import { Toaster } from '@/components/ui/toaster';
import { Providers } from './providers';
import { cn } from '@/lib/utils';
import './globals.css';

// Font configurations with Vietnamese support
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter'
});

const notoSans = Noto_Sans({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin', 'vietnamese'],
  variable: '--font-noto-sans'
});

export const metadata: Metadata = {
  title: 'TTMI ERP'
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();
  const requestCookies = cookies();
  const sid = requestCookies.get('sid')?.value;
  const isLogedIn = sid && sid !== 'Guest';

  return (
    <html lang={locale} className={`${inter.variable} ${notoSans.variable}`}>
      <body className={cn('fixed flex h-screen w-screen flex-col font-sans')}>
        <Providers>
          <AritoAppBar />
          <main className='flex w-full flex-col overflow-auto bg-white'>
            {children}
            <Footer />
            <Toaster />
          </main>
        </Providers>
      </body>
    </html>
  );
}
